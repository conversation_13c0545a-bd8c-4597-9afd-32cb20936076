import React from 'react';
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";

const UserProfileSidebar = ({
  user,
  getAvatarUrl,
  handleAvatarError,
  isUploading,
  fileInputRef,
  handleAvatarUpload,
  getStatusColor,
  setShowPasswordModal,
  setShowRoleModal,
  formatDate
}) => {
  return (
    <div className="space-y-4 md:space-y-6">
      {/* Profile Card */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-4 md:p-6 flex flex-col items-center">
          <div className="relative group mb-3 md:mb-4">
            <div className="w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden border-4 border-white shadow-sm group-hover:opacity-90 transition-opacity">
              <img
                src={getAvatarUrl()}
                alt={`${user.first_name} ${user.last_name}`}
                className="w-full h-full object-cover"
                onError={handleAvatarError}
              />
              {isUploading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full">
                  <div className="animate-spin h-6 w-6 md:h-8 md:w-8 border-4 border-white border-t-transparent rounded-full"></div>
                </div>
              )}
            </div>

            <button
              onClick={() => fileInputRef.current?.click()}
              className="absolute bottom-0 right-0 bg-primary-500 text-white p-1 md:p-1.5 rounded-full shadow-lg hover:bg-primary-600 transition-colors"
              disabled={isUploading}
              title="Change profile picture"
            >
              <Icon icon="heroicons-outline:camera" className="h-3 w-3 md:h-4 md:w-4" />
            </button>

            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleAvatarUpload}
              disabled={isUploading}
            />
          </div>

          <h2 className="text-lg md:text-xl font-bold text-gray-800 text-center">
            {user.first_name} {user.last_name}
          </h2>

          <div className="mt-2 flex items-center justify-center flex-wrap gap-1 md:gap-2">
            <Badge color={getStatusColor(user.status)} className="capitalize text-xs py-0.5 px-2">
              {user.status || 'Unknown'}
            </Badge>
            <span className="text-gray-300 hidden sm:inline">•</span>
            <div className="flex flex-wrap justify-center gap-1">
              {user.role && (
                <Badge
                  color="gray"
                  className="capitalize text-xs py-0.5 px-2"
                >
                  {user.role}
                </Badge>
              )}
              {user.roles && Array.isArray(user.roles) && user.roles.map((role, idx) => (
                <Badge
                  key={idx}
                  color="gray"
                  className="capitalize text-xs py-0.5 px-2"
                >
                  {role}
                </Badge>
              ))}
            </div>
          </div>

          <div className="mt-3 w-full flex flex-col gap-2">
            <div className="flex items-center text-xs md:text-sm text-gray-500">
              <Icon icon="heroicons:envelope" className="h-3 w-3 md:h-4 md:w-4 mr-2 text-gray-400 flex-shrink-0" />
              <span className="truncate">{user.email}</span>
            </div>
            {user.phone_number && (
              <div className="flex items-center text-xs md:text-sm text-gray-500">
                <Icon icon="heroicons:phone" className="h-3 w-3 md:h-4 md:w-4 mr-2 text-gray-400 flex-shrink-0" />
                <span className="truncate">{user.phone_number}</span>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="">
          <div className="items-start justify-start">
            <Button
              variant="outline"
              className="justify-start py-0 text-sm text-blue-500 hover:underline "
              onClick={() => setShowPasswordModal(true)}
              icon="heroicons-outline:key"
              iconPosition="left"
            >
              <span className="">Change Password</span>
            </Button>
            <Button
              variant="outline"
              className="justify-start pt-0 text-sm text-blue-500 hover:underline "
              onClick={() => setShowRoleModal(true)}
              icon="heroicons-outline:user-group"
              iconPosition="left"
            >
              <span className="">Manage Roles</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Account Information Card */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-4 md:px-6 py-3 md:py-4 border-b border-gray-100">
          <h3 className="text-base md:text-lg font-semibold text-gray-900">Account Information</h3>
        </div>

        <div className="p-3 md:p-4">
          <div className="space-y-3 md:space-y-4">
            <div className="flex items-center gap-2 md:gap-3 pb-2 md:pb-3 border-b border-gray-100">
              <div className="bg-primary-50 rounded-full p-1.5 md:p-2 flex-shrink-0">
                <Icon icon="heroicons:identification" className="h-4 w-4 md:h-5 md:w-5 text-primary-500" />
              </div>
              <div className="min-w-0 flex-1">
                <span className="text-xs text-gray-500 block">User ID</span>
                <p className="text-xs md:text-sm font-medium text-gray-900 truncate">{user.id}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
              <div className="flex items-center gap-2 md:gap-3">
                <div className="bg-green-50 rounded-full p-1.5 md:p-2 flex-shrink-0">
                  <Icon icon="heroicons:calendar-days" className="h-4 w-4 md:h-5 md:w-5 text-green-500" />
                </div>
                <div className="min-w-0 flex-1">
                  <span className="text-xs text-gray-500 block">Created</span>
                  <p className="text-xs md:text-sm font-medium text-gray-900 truncate">{formatDate(user.created_at)}</p>
                </div>
              </div>

              <div className="flex items-center gap-2 md:gap-3">
                <div className="bg-blue-50 rounded-full p-1.5 md:p-2 flex-shrink-0">
                  <Icon icon="heroicons:arrow-path" className="h-4 w-4 md:h-5 md:w-5 text-blue-500" />
                </div>
                <div className="min-w-0 flex-1">
                  <span className="text-xs text-gray-500 block">Updated</span>
                  <p className="text-xs md:text-sm font-medium text-gray-900 truncate">{formatDate(user.updated_at)}</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2 md:gap-3 pt-2 md:pt-3 border-t border-gray-100">
              <div className="bg-purple-50 rounded-full p-1.5 md:p-2 flex-shrink-0">
                <Icon icon="heroicons:clock" className="h-4 w-4 md:h-5 md:w-5 text-purple-500" />
              </div>
              <div className="min-w-0 flex-1">
                <span className="text-xs text-gray-500 block">Last Login</span>
                <p className="text-xs md:text-sm font-medium text-gray-900 truncate">{formatDate(user.last_login_at) || 'Never'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileSidebar;
