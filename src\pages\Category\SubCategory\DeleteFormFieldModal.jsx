import React from 'react';
import { useDeleteApiMutation } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import { toast } from 'react-toastify';
import { FiAlertTriangle, FiX, FiTrash2 } from 'react-icons/fi';

const DeleteFormFieldModal = ({ showModal, setShowModal, formFieldId, formFieldName, onSuccess }) => {
  const [deleteFormField, { isLoading }] = useDeleteApiMutation();

  const handleDelete = async () => {
    try {
      const response = await deleteFormField({
        end_point: `/admin/form-fields/${formFieldId}`,
        body: { _method: 'DELETE' }
      });
      
      if (response.error) {
        console.error('Error:', response.error);
        toast.error('Failed to delete form field');
      } else {
        toast.success('Form field deleted successfully');
        setShowModal(false);
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('An unexpected error occurred');
    }
  };

  return (
    <Modal
      title="Delete Form Field"
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      centered
      themeClass="bg-red-500"
      className="max-w-2xl"
    >
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
            <FiAlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Delete Form Field</h3>
        <p className="text-sm text-gray-500 mb-6">
          Are you sure you want to delete the form field "{formFieldName}"? This action cannot be undone.
        </p>
        <div className="flex justify-center space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowModal(false)}
            className="h-9 px-4 flex items-center"
          >
            <FiX className="mr-2 w-4 h-4" /> Cancel
          </Button>
          <Button
            type="button"
            isLoading={isLoading}
            variant="danger"
            className="h-9 px-4 flex items-center"
            onClick={handleDelete}
          >
            <FiTrash2 className="mr-2 w-4 h-4" /> Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteFormFieldModal;
