import { useEffect, useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from "react-redux";
import { setUser, setToken, logOut } from "@/store/api/auth/authSlice";
import { refreshAccessToken } from '@/utils/tokenRefresh';
import { toast } from 'react-toastify';
import Loading from '@/components/Loading';

const PrivateOutlet = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuth, token } = useSelector((state) => state.auth);
  const [checkingAuth, setCheckingAuth] = useState(true);

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        // Get tokens from cookies
        const tokenFromCookie = document.cookie
          .split('; ')
          .find((cookie) => cookie.startsWith('accessToken='));

        const refreshTokenFromCookie = document.cookie
          .split('; ')
          .find((cookie) => cookie.startsWith('refreshToken='));

        // If we have a token in Redux or in cookies
        if (isAuth || token || tokenFromCookie) {
          const tokenValue = tokenFromCookie?.split('=')[1] || token;

          // Set the token in Redux if it's not already there
          if (tokenValue && !token) {
            dispatch(setToken(tokenValue));
          }

          // Get user data from localStorage
          const storedUser = localStorage.getItem('user');
          if (storedUser && !isAuth) {
            dispatch(setUser(JSON.parse(storedUser)));
          }
        }
        // If we don't have a token but have a refresh token, try to refresh
        else if (!tokenFromCookie && refreshTokenFromCookie) {
          const newToken = await refreshAccessToken();

          if (!newToken) {
            // If refresh fails, log the user out and redirect to login
            console.log('Token refresh failed in PrivateOutlet');
            toast.error("Your session has expired. Please log in again.");

            // Dispatch logout action to clear state and cookies
            dispatch(logOut());

            // Navigate to login page
            navigate('/', { replace: true });
          }
        }
        // If we have neither token, redirect to login
        else {
          navigate('/', { replace: true });
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
        navigate('/', { replace: true });
      } finally {
        setCheckingAuth(false);
      }
    };

    checkAuthentication();
  }, [isAuth, token, dispatch, navigate]);

  if (checkingAuth) return <Loading />; // Show loading spinner while checking auth

  return <Outlet />;
};

export default PrivateOutlet;
