import { useState } from 'react';
import { useField } from 'formik';

const KeywordsInput = ({ label, placeholder, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { value } = meta;
  const { setValue } = helpers;
  const [inputValue, setInputValue] = useState('');

  const keywords = value ? value.split(',').filter(k => k.trim() !== '') : [];

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const newKeyword = inputValue.trim();
      if (newKeyword && !keywords.includes(newKeyword)) {
        const updatedKeywords = [...keywords, newKeyword].join(',');
        setValue(updatedKeywords);
        setInputValue('');
      }
    }
  };

  const removeKeyword = (keywordToRemove) => {
    const updatedKeywords = keywords.filter(k => k !== keywordToRemove).join(',');
    setValue(updatedKeywords);
  };

  return (
    <div>
      {label && (
        <label htmlFor={props.name} className="block mb-1 font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
      )}
      <div className="flex flex-wrap items-center gap-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-transparent">
        {keywords.map((keyword, i) => (
          <div key={i} className="flex items-center bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full text-sm text-gray-900 dark:text-white">
            {keyword}
            <button
              type="button"
              onClick={() => removeKeyword(keyword)}
              className="ml-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400"
            >
              &times;
            </button>
          </div>
        ))}
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          className="flex-1 min-w-[100px] p-1 outline-none bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          placeholder={placeholder || "Type and press Enter"}
        />
      </div>
      {meta.touched && meta.error && (
        <p className="text-red-500 text-sm mt-1">{meta.error}</p>
      )}
    </div>
  );
};

export default KeywordsInput;
