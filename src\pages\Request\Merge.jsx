import React, { useState, useRef, useEffect } from 'react';
import { Formik, Form } from 'formik';
import { usePostApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import { useNavigate, useLocation } from 'react-router-dom';
import InputField from "@/components/ui/InputField";
import TextareaField from "@/components/ui/TextareaField";
import InputSelect from "@/components/ui/InputSelect";
import Card from "@/components/ui/Card";
import { toast } from 'react-toastify';
import Icon from "@/components/ui/Icon";
import Loading from '@/components/Loading';
import { getMergeInitialValues, mergeValidationSchema, URGENCY_OPTIONS, REQUEST_TYPE_OPTIONS } from "./FormSettings";

const MergeRequests = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const formikRef = useRef();
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [selectedRequestIds, setSelectedRequestIds] = useState([]);

  // Extract request IDs from URL params
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const ids = searchParams.get('ids');
    if (ids) {
      setSelectedRequestIds(ids.split(','));
    } else {
      // If no IDs provided, redirect back to requests
      navigate('/requests');
    }
  }, [location.search, navigate]);

  // Fetch categories for the dropdown
  const { 
    data: categoriesData, 
    isLoading: isLoadingCategories 
  } = useGetApiQuery('/admin/categories');

  // Track selected category for subcategories
  const [selectedCategoryId, setSelectedCategoryId] = useState('');

  // Get subcategories from the selected category
  const subcategoriesData = selectedCategoryId
    ? categoriesData?.data?.find(cat => cat.id === selectedCategoryId)?.sub_categories || []
    : [];

  console.log(categoriesData);

  const [postApi, { isLoading: isPostLoading }] = usePostApiMutation();

  const handleSubmit = async (values, { setErrors }) => {
    setFormSubmitting(true);
    
    try {
      const payload = {
        // New request data
        title: values.title,
        short_description: values.short_description || null,
        description: values.description || null,
        category_id: values.category_id,
        sub_category_id: values.sub_category_id,
        quantity: parseInt(values.quantity) || 1,
        budget_min: parseFloat(values.budget_min) || null,
        budget_max: parseFloat(values.budget_max) || null,
        deadline: values.deadline || null,
        urgency: values.urgency,
        request_type: values.request_type,
        location: values.location || null,
        additional_info: values.additional_info || null,
        
        // Request IDs to merge
        request_ids_to_merge: selectedRequestIds
      };

      const response = await postApi({
        end_point: '/admin/requests/merge',
        body: payload
      });

      if (response.error) {
        console.error('Error:', response.error);
        if (response.error.data && response.error.data.errors) {
          setErrors(response.error.data.errors);
        }
        toast.error(response.error.data?.message || 'Failed to merge requests');
      } else {
        
        navigate('/requests');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('An error occurred while merging requests');
    } finally {
      setFormSubmitting(false);
    }
  };

  if (isLoadingCategories) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (selectedRequestIds.length < 2) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Icon icon="heroicons:exclamation-triangle" className="w-16 h-16 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Request</h2>
        <p className="text-gray-600 mb-4">At least 2 requests are required for merging.</p>
        <Button
          variant="primary"
          onClick={() => navigate('/requests')}
        >
          Back to Requests
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Merge Requests</h1>
          <p className="text-sm text-gray-600 mt-1">
            Create a new request by merging {selectedRequestIds.length} selected requests
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/requests')}
            className="h-9 px-4 flex items-center"
          >
            <Icon icon="heroicons:arrow-left" className="mr-2 w-4 h-4" /> Back
          </Button>
        </div>
      </div>

      {/* Info Card */}
      <Card className="border-blue-200 bg-blue-50">
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <Icon icon="heroicons:information-circle" className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Merging {selectedRequestIds.length} Requests
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  You are creating a new request that will merge {selectedRequestIds.length} selected requests. 
                  The original requests will be marked as merged and linked to this new request.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Form */}
      <Card className="max-w-4xl">
        <div className="p-6">
          <Formik
            innerRef={formikRef}
            initialValues={getMergeInitialValues()}
            validationSchema={mergeValidationSchema}
            onSubmit={handleSubmit}
            validateOnBlur
            validateOnChange
            validateOnMount
          >
            {({ values, setFieldValue, handleBlur, touched, errors, setTouched, validateForm }) => (
              <Form className="space-y-6">
                {/* Error Summary */}
                {submitAttempted && Object.keys(errors).length > 0 && (
                  <Card className="border-red-200 bg-red-50">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-0.5">
                        <Icon icon="heroicons:exclamation-circle" className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                          Please fix the following errors:
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                          <ul className="list-disc list-inside space-y-1">
                            {Object.entries(errors).map(([field, error]) => (
                              <li key={field}>
                                {field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}: {error}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </Card>
                )}

                {/* Basic Information */}
                <Card>
                  <div className="p-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">New Request Details</h3>
                    <p className="text-sm text-gray-600">Enter the details for the merged request</p>
                  </div>
                  <div className="p-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Title */}
                      <div className="md:col-span-2">
                        <InputField
                          name="title"
                          label="Title"
                          required
                          placeholder="Enter merged request title"
                        />
                      </div>

                      {/* Short Description */}
                      <div className="md:col-span-2">
                        <TextareaField
                          name="short_description"
                          label="Short Description"
                          placeholder="Brief description of the merged request"
                          rows={2}
                        />
                      </div>

                      {/* Description */}
                      <div className="md:col-span-2">
                        <TextareaField
                          name="description"
                          label="Description"
                          placeholder="Detailed description of the merged request"
                          rows={4}
                        />
                      </div>

                      {/* Category */}
                      <div>
                        <InputSelect
                          name="category_id"
                          label="Category"
                          options={categoriesData?.data?.map(category => ({
                            value: category.id,
                            label: category.title
                          })) || []}
                          required
                          onRoleSelect={(value) => {
                            setFieldValue('sub_category_id', ''); // Reset subcategory
                            setSelectedCategoryId(value);
                          }}
                        />
                      </div>

                      {/* Sub Category */}
                      <div>
                        <InputSelect
                          name="sub_category_id"
                          label="Sub Category"
                          options={subcategoriesData?.map(subcategory => ({
                            value: subcategory.id,
                            label: subcategory.title
                          })) || []}
                          required
                          isDisabled={!selectedCategoryId}
                        />
                      </div>

                      {/* Quantity */}
                      <div>
                        <InputField
                          name="quantity"
                          label="Quantity"
                          type="number"
                          min="1"
                          placeholder="1"
                        />
                      </div>

                      {/* Budget Min */}
                      <div>
                        <InputField
                          name="budget_min"
                          label="Minimum Budget"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                        />
                      </div>

                      {/* Budget Max */}
                      <div>
                        <InputField
                          name="budget_max"
                          label="Maximum Budget"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                        />
                      </div>

                      {/* Deadline */}
                      <div>
                        <InputField
                          name="deadline"
                          label="Deadline"
                          type="date"
                        />
                      </div>

                      {/* Urgency */}
                      <div>
                        <InputSelect
                          name="urgency"
                          label="Urgency"
                          options={URGENCY_OPTIONS}
                          required
                        />
                      </div>

                      {/* Request Type */}
                      <div>
                        <InputSelect
                          name="request_type"
                          label="Request Type"
                          options={REQUEST_TYPE_OPTIONS}
                          required
                        />
                      </div>

                      {/* Location */}
                      <div className="md:col-span-2">
                        <InputField
                          name="location"
                          label="Location"
                          placeholder="Enter location"
                        />
                      </div>

                      {/* Additional Info */}
                      <div className="md:col-span-2">
                        <TextareaField
                          name="additional_info"
                          label="Additional Information"
                          placeholder="Any additional information about the merged request"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Form Actions */}
                <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/requests')}
                    disabled={formSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    variant="primary"
                    isLoading={formSubmitting || isPostLoading}
                    onClick={async () => {
                      setSubmitAttempted(true);
                      const touchedFields = {};
                      Object.keys(values).forEach(key => {
                        touchedFields[key] = true;
                      });
                      setTouched(touchedFields);

                      const errors = await validateForm();
                      if (Object.keys(errors).length === 0) {
                        formikRef.current.handleSubmit();
                      }
                    }}
                  >
                    Merge {selectedRequestIds.length} Requests
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Card>
    </div>
  );
};

export default MergeRequests;
