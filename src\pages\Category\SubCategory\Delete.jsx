import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
const Delete = ({showDeleteModal, setShowDeleteModal, data}) => {


    const [deleteApi, { isLoading, isError, error, isSuccess }] = useDeleteApiMutation();
    const onSubmit = async () => {
        const response = await deleteApi({end_point: '/admin/subcategories/' + data?.id, body: {}})
        console.log(response);
        setShowDeleteModal(false);
    }
    return (

    <Modal
    activeModal={showDeleteModal}
    onClose={() => setShowDeleteModal(false)}
    title="Delete Sub Category"
    className="max-w-2xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowDeleteModal(false)}
        />
        }
    >        

    <h3 className="text-center">Are you sure?</h3>
    <p className="text-center text-slate-500 text-sm mt-4">You are going to delete the <b>"{data?.title}"</b> sub category. 
    Once you do this, there will be no going back.</p>

    <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button 
            // isLoading={isLoading}
            type="button"
            className="btn text-center btn-primary mr-4"
            onClick={() => setShowDeleteModal(false)} 
        >
            Cancel
        </Button>
        <Button 
            isLoading={isLoading}
            type="button"
            className="btn text-center btn-danger"
            onClick={onSubmit} 
        >
            Delete
        </Button>
    </div>
    {/* <Button text="Delete" btnClass="btn btn-danger" /> */}
    </Modal>
    );
}

export default Delete;
