import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";

const UserHeader = ({ user }) => {
  const navigate = useNavigate();
  
  return (
    <div className="mb-6">
      <div className="flex items-center text-sm text-gray-500 mb-2">
        <button onClick={() => navigate('/users')} className="hover:text-primary-600 transition-colors">
          Users
        </button>
        <Icon icon="heroicons:chevron-right" className="h-4 w-4 mx-2" />
        <span className="text-gray-700 font-medium">{user.first_name} {user.last_name}</span>
      </div>
      
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">User Profile</h1>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 border-gray-300 hover:bg-gray-50"
            icon="heroicons:arrow-left"
          >
            Back
          </Button>
          <Button
            variant="primary"
            onClick={() => navigate(`/users/edit/${user.id}`)}
            className="flex items-center gap-2"
            icon="heroicons:pencil-square"
          >
            Edit Profile
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
