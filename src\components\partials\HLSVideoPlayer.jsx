import React, { useEffect, useRef } from 'react';
import Hls from 'hls.js';

const HLSVideoPlayer = ({ videoUrl}) => {
  const videoRef = useRef(null);

  useEffect(() => {
    if (Hls.isSupported()) {
      const hls = new Hls();
      hls.loadSource(videoUrl);
      hls.attachMedia(videoRef.current);

      hls.on(Hls.Events.MANIFEST_PARSED, function () {
        videoRef.current.play();
      });

      return () => {
        hls.destroy();
      };
    } else if (videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
      videoRef.current.src = videoUrl;
      videoRef.current.addEventListener('loadedmetadata', function () {
        videoRef.current.play();
      });
    }
  }, [videoUrl]);

  return (
    <div className="video-player-container">
      <video ref={videoRef} controls width="100%" height="500px">
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default HLSVideoPlayer;
