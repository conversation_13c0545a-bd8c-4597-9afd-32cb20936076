import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";
import TextArea from "@/components/ui/Textarea";

const ApproveModal = ({ showApproveModal, setShowApproveModal, data, onSuccess }) => {
    const [updateApi, { isLoading }] = useUpdateApiMutation();
    const [notes, setNotes] = useState("");

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    const onSubmit = async () => {
        try {
            const response = await updateApi({
                end_point: '/admin/offers/' + data?.id + '/status',
                body: {
                    status: 'Approved',
                    reason: notes.trim() || 'This offer meets our requirements'
                }
            });

            if (!response.error) {
                setShowApproveModal(false);
                // Call the onSuccess callback to refresh the data
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess();
                }
            } else {
                toast.error(response.error?.data?.message || "Failed to approve offer");
            }
        } catch (error) {
            toast.error("An error occurred while approving the offer");
            console.error(error);
        }
    };

    return (
        <Modal
            title="Approve Offer"
            themeClass="bg-success-500 dark:bg-success-700"
            centered={true}
            className="max-w-md"
            activeModal={showApproveModal}
            onClose={() => setShowApproveModal(false)}
        >
            <div className="text-base text-gray-700 dark:text-slate-300">
                <div className="flex items-center mb-6 bg-success-50 p-4 rounded-lg border border-success-100">
                    <div className="mr-4">
                        <div className="h-12 w-12 rounded-full bg-success-100 flex items-center justify-center">
                            <Icon icon="heroicons:check" className="h-6 w-6 text-success-600" />
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Confirm Approval</h3>
                        <p className="text-sm text-gray-600">Are you sure you want to approve this offer?</p>
                    </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-100">
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Offer Details</h4>
                    <div className="space-y-3">
                        {data?.title && (
                            <div className="flex justify-between text-sm">
                                <span className="font-medium text-gray-600">Title:</span>
                                <span className="text-gray-800">{data.title}</span>
                            </div>
                        )}
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Price:</span>
                            <span className="text-gray-800 font-semibold">{formatCurrency(data?.price || 0)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Delivery Time:</span>
                            <span className="text-gray-800">{data?.delivery_time} days</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Seller:</span>
                            <span className="text-gray-800">
                                {data?.seller?.first_name} {data?.seller?.last_name}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Approval Notes (Optional)
                    </label>
                    <TextArea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="Add any notes about this approval..."
                        className="w-full"
                        rows={3}
                    />
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                    onClick={() => setShowApproveModal(false)}
                    icon="heroicons:x-mark"
                >
                    Cancel
                </Button>
                <Button
                    variant="success"
                    onClick={onSubmit}
                    isLoading={isLoading}
                    icon="heroicons:check"
                >
                    Approve Offer
                </Button>
            </div>
        </Modal>
    );
};

export default ApproveModal;
