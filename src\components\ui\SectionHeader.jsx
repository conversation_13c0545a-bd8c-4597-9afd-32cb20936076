import React from "react";

const SectionHeader = ({ title, subtitle, description, badge }) => {
    return (
        <div className="flex justify-between items-start mb-4 pb-3 border-b border-gray-200">
            <div>
                <h2 className="text-sm font-medium text-gray-700">{title}</h2>
                {(subtitle || description) && (
                    <p className="text-xs text-gray-500 mt-1">{subtitle || description}</p>
                )}
            </div>
            {badge && (
                <div>
                    {badge}
                </div>
            )}
        </div>
    );
};

export default SectionHeader;
