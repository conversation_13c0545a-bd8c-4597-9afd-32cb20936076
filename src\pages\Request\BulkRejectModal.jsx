import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import TextArea from "@/components/ui/Textarea";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const BulkRejectModal = ({ showModal, setShowModal, selectedRequests, onSuccess }) => {
    const [postApi, { isLoading }] = usePostApiMutation();
    const [rejectionReason, setRejectionReason] = useState("");

    const onSubmit = async () => {
        try {
            const requestBody = {
                request_ids: selectedRequests,
                action: 'reject'
            };

            // Add note only if provided
            if (rejectionReason.trim()) {
                requestBody.note = rejectionReason.trim();
            }

            const response = await postApi({
                end_point: '/admin/requests/bulk-action',
                body: requestBody
            });

            if (!response.error) {
                setShowModal(false);
                setRejectionReason("");
                if (onSuccess) onSuccess();
            } else {
                toast.error(response.error?.data?.message || "Failed to reject requests");
            }
        } catch (error) {
            toast.error("An error occurred while rejecting requests");
            console.error(error);
        }
    };

    return (
        <Modal
            title="Bulk Reject Requests"
            themeClass="bg-danger-500 dark:bg-danger-700"
            centered={true}
            className="max-w-md"
            activeModal={showModal}
            onClose={() => setShowModal(false)}
        >
            <div className="text-base text-gray-700 dark:text-slate-300">
                <div className="flex items-center mb-6 bg-danger-50 p-4 rounded-lg border border-danger-100">
                    <div className="mr-4">
                        <div className="h-12 w-12 rounded-full bg-danger-100 flex items-center justify-center">
                            <Icon icon="heroicons:x-mark" className="h-6 w-6 text-danger-600" />
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Confirm Bulk Rejection</h3>
                        <p className="text-sm text-gray-600">
                            Are you sure you want to reject {selectedRequests.length} selected request(s)?
                        </p>
                    </div>
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Rejection Reason (Optional)
                    </label>
                    <TextArea
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        placeholder="Provide reason for rejecting these requests..."
                        className="w-full"
                        rows={3}
                    />
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                    onClick={() => setShowModal(false)}
                    icon="heroicons:x-mark"
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onSubmit}
                    isLoading={isLoading}
                    icon="heroicons:x-mark"
                >
                    Reject {selectedRequests.length} Request(s)
                </Button>
            </div>
        </Modal>
    );
};

export default BulkRejectModal;
