stages:  
  - build  
  - deploy  

variables:  
  NODE_ENV: development  # Vite does not use NODE_ENV="production" at build time  
  DEPLOY_PATH: "/var/www/Marktzoom-admin"  

build:  
  stage: build  
  script:  
    - echo "Providing environment variables for Vite build..."  
    - |  
      cat <<EOL > .env  
      VITE_HOST_URL=https://api-dev.marktzoom.com/api/  
      VITE_ASSET_HOST_URL=https://api-dev.marktzoom.com  
      EOL  
    - echo "Installing Node dependencies..."  
    - yarn install                # Or 'yarn install' if you use yarn.lock!  
    - echo "Building frontend..."  
    - yarn run build              # Or 'yarn build'  
  artifacts:  
    paths:  
      - dist/  
    expire_in: 1 hour  

deploy:  
  stage: deploy  
  script:  
    - echo "Deploying to $DEPLOY_PATH..."  
    - sudo mkdir -p $DEPLOY_PATH  
    - echo "Syncing built files with rsync..."  
    - sudo rsync -avz --delete dist/ $DEPLOY_PATH/  
    - echo "Fixing ownership..."  
    - sudo chown -R www-data:www-data $DEPLOY_PATH  
    - echo "Deployment complete."  
  dependencies:  
    - build