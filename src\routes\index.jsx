
import { Suspense } from "react";
import { Route, Routes } from "react-router-dom";

import PrivateOutlet from "./PrivateOutlet";
import PublicOutlet from "./PublicOutlet";
import Loading from "@/components/Loading";

// Layouts
import AuthLayout from "@/layout/AuthLayout";
import Layout from "@/layout/Layout";

// Auth Pages
import Login from "@/pages/auth/login";
import ForgotPass from "@/pages/auth/forgot-password";
import Error from "@/pages/404";

// App Pages
import Dashboard from "@/pages/Dashboard";
import Categories from "@/pages/Category";
import CategoriesCreateOrUpdate from "@/pages/Category/CreateOrUpdate";
import CategoriesDetails from "@/pages/Category/Details";
import SubCategoryDetails from "@/pages/Category/SubCategory/Details";
import BulkUploadSubcategories from "@/pages/Category/SubCategory/BulkUpload";
import Request from "@/pages/Request";
import RequestDetails from "@/pages/Request/Details";
import Offer from "@/pages/Offer";
import OfferDetails from "@/pages/Offer/Details";
import User from "@/pages/User";
import UserDetails from "@/pages/User/Details";
import UserCreateOrUpdate from "@/pages/User/CreateOrUpdate";
import Order from "@/pages/Order";
import OrderDetails from "@/pages/Order/Details";
import Subscription from "@/pages/Subscription";
import SubscriptionCreateOrUpdate from "@/pages/Subscription/CreateOrUpdate";
import SubscriptionDetails from "@/pages/Subscription/Details";

const AppRoutes = () => {
  return (
    <Routes>
      <Route element={<PublicOutlet />}>
        <Route path="/" element={<AuthLayout />}>
          <Route path="/" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPass />} />
        </Route>
      </Route>

      <Route element={<PrivateOutlet />}>
        <Route path="/" element={<Layout />}>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/categories" element={<Categories />} />
          <Route path="/categories/:status" element={<Categories />} />
          <Route path="/category/create" element={<CategoriesCreateOrUpdate />} />
          <Route path="/categories/edit/:id" element={<CategoriesCreateOrUpdate />} />
          <Route path="/category/:id" element={<CategoriesDetails />} />
          <Route path="/category/:categoryId/bulk-upload" element={<BulkUploadSubcategories />} />
          <Route path="/subcategory/:id" element={<SubCategoryDetails />} />
          <Route path="/requests" element={<Request />} />
          <Route path="/requests/:statusParam" element={<Request />} />
          <Route path="/request/:id" element={<RequestDetails />} />
          <Route path="/offers" element={<Offer />} />
          <Route path="/offers/:statusParam" element={<Offer />} />
          <Route path="/offer/:id" element={<OfferDetails />} />
          <Route path="/user/:id" element={<UserDetails />} />
          <Route path="/users/:role" element={<User />} />
          <Route path="/users/create" element={<UserCreateOrUpdate />} />
          <Route path="/users/edit/:id" element={<UserCreateOrUpdate />} />
          <Route path="/orders" element={<Order />} />
          <Route path="/orders/:statusParam" element={<Order />} />
          <Route path="/order/:id" element={<OrderDetails />} />
          <Route path="/subscriptions" element={<Subscription />} />
          <Route path="/subscriptions/:status" element={<Subscription />} />
          <Route path="/subscription/create" element={<SubscriptionCreateOrUpdate />} />
          <Route path="/subscription/edit/:id" element={<SubscriptionCreateOrUpdate />} />
          <Route path="/subscription/:id" element={<SubscriptionDetails />} />
        </Route>
      </Route>

      <Route
        path="/404"
        element={
          <Suspense fallback={<Loading />}>
            <Error />
          </Suspense>
        }
      />
    </Routes>
  );
};

export default AppRoutes;

