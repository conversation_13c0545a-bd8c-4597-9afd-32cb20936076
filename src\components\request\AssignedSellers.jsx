import React from "react";
import Icon from "@/components/ui/Icon";
import { Link } from "react-router-dom";

const AssignedSellers = ({ assignedSellers }) => {
    if (!assignedSellers?.length) return null;

    // Function to get status color
    const getStatusColor = (status) => {
        if (!status) return "gray";

        switch (status.toLowerCase()) {
            case 'accepted':
                return "success";
            case 'rejected':
                return "danger";
            case 'pending':
                return "warning";
            default:
                return "gray";
        }
    };

    // Function to get status icon
    const getStatusIcon = (status) => {
        if (!status) return "heroicons-outline:question-mark-circle";

        switch (status.toLowerCase()) {
            case 'accepted':
                return "heroicons-outline:check-circle";
            case 'rejected':
                return "heroicons-outline:x-circle";
            case 'pending':
                return "heroicons-outline:clock";
            default:
                return "heroicons-outline:information-circle";
        }
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <div className="bg-white rounded-xl shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-800">Assigned Sellers</h2>
                <span className="bg-primary-50 text-primary-700 text-sm font-medium px-3 py-1 rounded-full">
                    {assignedSellers.length} {assignedSellers.length === 1 ? 'Seller' : 'Sellers'}
                </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {assignedSellers.map((assignment) => (
                    <div
                        key={assignment.id}
                        className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200"
                    >
                        {/* Header with seller info */}
                        <div className="flex items-center p-4 border-b border-gray-100 bg-gray-50">
                            <div className="h-12 w-12 flex-shrink-0">
                                <img
                                    className="h-12 w-12 rounded-full object-cover border-2 border-white shadow"
                                    src={assignment.seller.profile_picture_url
                                        ? `${import.meta.env.VITE_ASSET_HOST_URL}${assignment.seller.profile_picture_url}`
                                        : '/images/avatar/default-avatar.png'
                                    }
                                    alt={`${assignment.seller.first_name} ${assignment.seller.last_name}`}
                                />
                            </div>
                            <div className="ml-4 flex-1">
                                <Link
                                    to={`/user/${assignment.seller.id}`}
                                    className="text-base font-medium text-gray-900 hover:text-primary-600 hover:underline"
                                >
                                    {assignment.seller.first_name} {assignment.seller.last_name}
                                </Link>
                                <div className="text-sm text-gray-500 flex items-center">
                                    <Icon icon="heroicons-outline:mail" className="h-3 w-3 mr-1" />
                                    {assignment.seller.email}
                                </div>
                            </div>
                            <div className={`px-3 py-1 rounded-full text-xs font-medium bg-${getStatusColor(assignment.status)}-100 text-${getStatusColor(assignment.status)}-800 flex items-center`}>
                                <Icon
                                    icon={getStatusIcon(assignment.status)}
                                    className="h-3 w-3 mr-1"
                                />
                                {assignment.status}
                            </div>
                        </div>

                        {/* Body with assignment details */}
                        <div className="p-4">
                            <div className="grid grid-cols-2 gap-4 mb-3">
                                <div>
                                    <div className="text-xs text-gray-500 mb-1">Assigned By</div>
                                    <div className="text-sm font-medium text-gray-800 flex items-center">
                                        <Icon icon="heroicons-outline:user" className="h-4 w-4 mr-1 text-gray-400" />
                                        {assignment.assigner.first_name} {assignment.assigner.last_name}
                                    </div>
                                </div>
                                <div>
                                    <div className="text-xs text-gray-500 mb-1">Assigned At</div>
                                    <div className="text-sm font-medium text-gray-800 flex items-center">
                                        <Icon icon="heroicons-outline:calendar" className="h-4 w-4 mr-1 text-gray-400" />
                                        {formatDate(assignment.assigned_at)}
                                    </div>
                                </div>
                            </div>

                            {/* Offer details */}
                            {assignment?.seller_offers?.length > 0 && (
                                <div className="mt-4 bg-gray-50 p-3 rounded-lg">
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="text-sm font-medium text-gray-700 flex items-center">
                                            <Icon icon="heroicons-outline:currency-dollar" className="h-4 w-4 mr-1 text-primary-500" />
                                            Offer Details
                                        </div>
                                        <Link
                                            to={`/offer/${assignment.seller_offers[0].id}`}
                                            className="text-xs text-primary-600 hover:underline flex items-center"
                                        >
                                            View Offer
                                            <Icon icon="heroicons-outline:arrow-right" className="h-3 w-3 ml-1" />
                                        </Link>
                                    </div>
                                    <div className="grid grid-cols-2 gap-2">
                                        <div className="bg-white p-2 rounded border border-gray-100">
                                            <div className="text-xs text-gray-500">Price</div>
                                            <div className="text-sm font-bold text-primary-600">
                                                ${assignment.seller_offers[0].price}
                                            </div>
                                        </div>
                                        <div className="bg-white p-2 rounded border border-gray-100">
                                            <div className="text-xs text-gray-500">Delivery Time</div>
                                            <div className="text-sm font-bold text-gray-800">
                                                {assignment.seller_offers[0].delivery_time} days
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Notes */}
                            {assignment.notes && (
                                <div className="mt-3 text-sm">
                                    <div className="text-xs text-gray-500 mb-1 flex items-center">
                                        <Icon icon="heroicons-outline:annotation" className="h-3 w-3 mr-1" />
                                        Notes
                                    </div>
                                    <div className="bg-yellow-50 p-2 rounded text-gray-700 border border-yellow-100">
                                        {assignment.notes}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default AssignedSellers;