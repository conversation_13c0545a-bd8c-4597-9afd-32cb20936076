import React from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { logOut } from '@/store/api/auth/authSlice';
import { toast } from 'react-toastify';

/**
 * A reusable logout button component
 * @param {Object} props - Component props
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.children - Button content
 * @param {Function} props.onLogoutSuccess - Callback function called after successful logout
 */
const LogoutButton = ({ 
  className = '', 
  children = 'Logout', 
  onLogoutSuccess = null 
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleLogout = () => {
    try {
      // Dispatch logout action to clear state and cookies
      dispatch(logOut());
      
      // Show success message
      toast.success('Logged out successfully');
      
      // Call the success callback if provided
      if (onLogoutSuccess && typeof onLogoutSuccess === 'function') {
        onLogoutSuccess();
      } else {
        // Navigate to login page
        navigate('/', { replace: true });
      }
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Error during logout. Please try again.');
    }
  };

  return (
    <button 
      onClick={handleLogout}
      className={`${className}`}
      type="button"
    >
      {children}
    </button>
  );
};

export default LogoutButton;
