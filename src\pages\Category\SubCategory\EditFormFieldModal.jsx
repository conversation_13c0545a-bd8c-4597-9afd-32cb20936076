import React, { useState, useEffect } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { useUpdateApiMutation } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import InputField from '@/components/ui/InputField';
import { toast } from 'react-toastify';
import { FiX, FiSave } from 'react-icons/fi';

const EditFormFieldModal = ({ showModal, setShowModal, formField, onSuccess }) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // Define validation schema
  const validationSchema = Yup.object().shape({
    label_name: Yup.string().required('Label name is required').max(255, 'Label name must be less than 255 characters'),
    input_type: Yup.string().required('Input type is required'),
    label_subtitle: Yup.string().nullable(),
    placeholder: Yup.string().nullable(),
    is_required: Yup.boolean(),

    options: Yup.array().when('input_type', {
      is: (type) => ['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(type),
      then: Yup.array().of(Yup.string()).min(1, 'At least one option is required'),
      otherwise: Yup.array()
    })
  });

  // Get initial values from the formField prop
  const getInitialValues = () => {
    return {
      label_name: formField?.label_name || '',
      input_type: formField?.input_type || 'TEXT',
      label_subtitle: formField?.label_subtitle || '',
      placeholder: formField?.placeholder || '',
      is_required: formField?.is_required || false,
      sort_order: formField?.sort_order || 1,
      options: formField?.options || []
    };
  };

  const handleSubmit = async (values, { setErrors }) => {
    try {
      const response = await updateApi({
        end_point: `/admin/form-fields/${formField.id}`,
        body: values
      });

      if (response.error) {
        console.error('Error:', response.error);
        if (response.error.data && response.error.data.errors) {
          setErrors(response.error.data.errors);
        }
        toast.error('Failed to update form field');
      } else {
        setShowModal(false);
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('An unexpected error occurred');
    }
  };

  return (
    <Modal
      title="Edit Form Field"
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      centered
      className="max-w-3xl"
    >
      <Formik
        initialValues={getInitialValues()}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, errors, touched, setFieldValue, handleBlur }) => (
          <Form className="space-y-4">
            {/* Error Summary */}
            {submitAttempted && Object.keys(errors).length > 0 && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-md" role="alert">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">There were errors with your submission</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <ul className="list-disc pl-5 space-y-1">
                        {Object.entries(errors).map(([field, error]) => (
                          typeof error === 'string' ? (
                            <li key={field}>{error}</li>
                          ) : null
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                name="label_name"
                label="Field Label"
                type="text"
                required
                placeholder="e.g. Email Address"
                onBlur={handleBlur}
                error={submitAttempted && errors.label_name}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Field Type</label>
                <Field
                  as="select"
                  name="input_type"
                  className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="TEXT">Text</option>
                  <option value="TEXTAREA">Textarea</option>
                  <option value="NUMBER">Number</option>
                  <option value="EMAIL">Email</option>
                  <option value="PASSWORD">Password</option>
                  <option value="DATE">Date</option>
                  <option value="TIME">Time</option>
                  <option value="DATETIME">Date & Time</option>
                  <option value="CHECKBOX">Checkbox</option>
                  <option value="RADIO">Radio</option>
                  <option value="SELECT">Select</option>
                  <option value="MULTISELECT">Multi-select</option>
                  <option value="FILE">File</option>
                  <option value="PHONE">Phone</option>
                  <option value="URL">URL</option>
                  <option value="COLOR">Color</option>
                  <option value="RANGE">Range</option>
                </Field>
                {submitAttempted && errors.input_type && (
                  <div className="text-red-500 text-xs mt-1">{errors.input_type}</div>
                )}
              </div>
            </div>

            <InputField
              name="label_subtitle"
              label="Field Description"
              type="text"
              placeholder="Optional description or help text"
              onBlur={handleBlur}
              error={submitAttempted && errors.label_subtitle}
            />

            <InputField
              name="placeholder"
              label="Placeholder Text"
              type="text"
              placeholder="e.g. Enter your email address"
              onBlur={handleBlur}
              error={submitAttempted && errors.placeholder}
            />

            <div className="flex items-center">
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name="is_required"
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm font-medium text-gray-700">Required Field</span>
              </label>
            </div>

            {/* Options for select, radio, checkbox */}
            {['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(values.input_type) && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Options</label>
                <div className="space-y-2">
                  {values.options.map((option, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <input
                        type="text"
                        value={option}
                        onChange={(e) => {
                          const newOptions = [...values.options];
                          newOptions[index] = e.target.value;
                          setFieldValue('options', newOptions);
                        }}
                        className="flex-1 h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Option value"
                      />
                      <button
                        type="button"
                        className="text-red-500 hover:text-red-700 p-1"
                        onClick={() => {
                          const newOptions = [...values.options];
                          newOptions.splice(index, 1);
                          setFieldValue('options', newOptions);
                        }}
                      >
                        <FiX className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded hover:bg-gray-200 flex items-center"
                    onClick={() => {
                      setFieldValue('options', [...values.options, '']);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Add Option
                  </button>
                </div>
                {submitAttempted && errors.options && (
                  <div className="text-red-500 text-xs mt-1">{errors.options}</div>
                )}
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowModal(false)}
                className="h-9 px-4 flex items-center"
              >
                <FiX className="mr-2 w-4 h-4" /> Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isLoading}
                variant="primary"
                className="h-9 px-4 flex items-center"
                onClick={() => {
                  setSubmitAttempted(true);
                }}
              >
                <FiSave className="mr-2 w-4 h-4" /> Update Field
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditFormFieldModal;
