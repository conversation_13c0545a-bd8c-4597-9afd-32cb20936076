import React, { useState, useEffect, useMemo } from "react";
import { useLocation, NavLink } from "react-router-dom";
import { menuItems } from "@/constant/data";
import Icon from "@/components/ui/Icon";

const Breadcrumbs = () => {
  const location = useLocation();

  // Parse the path segments using useMemo to avoid recalculation on every render
  const pathSegments = useMemo(() => {
    return location.pathname.split('/').filter(segment => segment);
  }, [location.pathname]);

  const [isHide, setIsHide] = useState(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState([]);

  // Format ID to be more readable
  const formatId = (id) => {
    // Check if it's a UUID or long ID
    // if (id && id.length > 12) {
    //   return `#${id.substring(0, 8)}...`;
    // }
    // return `#${id}`;
    return "Details";
  };

  useEffect(() => {
    // Initialize breadcrumb items
    const items = [];
    let shouldHide = false;

    // Check if we have a path
    if (pathSegments.length === 0) {
      setBreadcrumbItems([]);
      setIsHide(true);
      return;
    }

    // Check if this is a detail page with an ID parameter
    const lastSegment = pathSegments[pathSegments.length - 1];
    const isDetailPage = pathSegments.length >= 2 &&
      (
        !isNaN(lastSegment) || // Numeric ID
        (lastSegment.length > 8 && lastSegment.includes('-')) // UUID format
      );

    // Check if this is an edit page
    const isEditPage = pathSegments.length >= 3 && pathSegments[pathSegments.length - 2] === "edit";

    // For edit pages, we need to determine if it's a user edit page
    const isUserEditPage = isEditPage && pathSegments[0] === "users";

    // Find the parent menu item
    let parentItem = null;
    let childItem = null;

    // For simple routes like /dashboard
    if (pathSegments.length === 1) {
      const menuItem = menuItems.find(item => item.link === `/${pathSegments[0]}`);
      if (menuItem) {
        items.push({
          title: menuItem.title,
          link: menuItem.link,
          isActive: true
        });
        shouldHide = menuItem.isHide || false;
      }
    }
    // For child routes like /users/buyer or detail pages like /user/123
    else {
      // Try to find parent menu item
      parentItem = menuItems.find(item => item.link === `/${pathSegments[0]}`);

      if (parentItem) {
        items.push({
          title: parentItem.title,
          link: parentItem.link,
          isActive: false
        });

        // If it's a detail or edit page
        if (isDetailPage || isEditPage) {
          // For edit pages like /users/edit/123
          if (isEditPage) {
            // For user edit pages, add a "Details" item before "Edit"
            if (pathSegments[0] === "users") {
              items.push({
                title: "Details",
                link: `/user/${lastSegment}`, // Link to the user details page
                isActive: false
              });
            }

            items.push({
              title: "Edit",
              link: null,
              isActive: true
            });
          } else {
            // For detail pages, add the ID as the last item
            items.push({
              title: formatId(lastSegment),
              link: null,
              isActive: true
            });
          }
        }
        // For child routes like /users/buyer
        else if (parentItem.child) {
          const childPath = `/${pathSegments.join('/')}`;
          childItem = parentItem.child.find(child => child.childlink === childPath);

          if (childItem) {
            items.push({
              title: childItem.childtitle,
              link: childItem.childlink,
              isActive: true
            });
          } else {
            // If no exact match, just use the last segment as title
            items.push({
              title: pathSegments[pathSegments.length - 1],
              link: null,
              isActive: true
            });
          }
        }
      } else {
        // If no parent found, just use the path segments
        items.push({
          title: pathSegments[0],
          link: `/${pathSegments[0]}`,
          isActive: pathSegments.length === 1
        });

        if (pathSegments.length > 1) {
          // If it's a detail page
          if (isDetailPage) {
            items.push({
              title: formatId(lastSegment),
              link: null,
              isActive: true
            });
          } else {
            items.push({
              title: pathSegments[pathSegments.length - 1],
              link: null,
              isActive: true
            });
          }
        }
      }
    }

    setBreadcrumbItems(items);
    setIsHide(shouldHide);
  }, [location.pathname]);

  if (isHide || breadcrumbItems.length === 0) {
    return null;
  }

  return (
    <div className="md:mb-6 mb-4 flex space-x-3 rtl:space-x-reverse">
      <ul className="breadcrumbs">
        {/* Home icon */}
        <li className="text-primary-500">
          <NavLink to="/dashboard" className="text-lg">
            <Icon icon="heroicons-outline:home" />
          </NavLink>
          {breadcrumbItems.length > 0 && (
            <span className="breadcrumbs-icon rtl:transform rtl:rotate-180">
              <Icon icon="heroicons:chevron-right" />
            </span>
          )}
        </li>

        {/* Breadcrumb items */}
        {breadcrumbItems.map((item, index) => (
          <li
            key={index}
            className={`${
              item.isActive
                ? "text-slate-500 dark:text-slate-400"
                : "text-primary-500"
            } capitalize`}
          >
            {!item.isActive && item.link ? (
              <>
                <NavLink to={item.link} className="capitalize">
                  {item.title}
                </NavLink>
                <span className="breadcrumbs-icon rtl:transform rtl:rotate-180">
                  <Icon icon="heroicons:chevron-right" />
                </span>
              </>
            ) : (
              item.title
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Breadcrumbs;
