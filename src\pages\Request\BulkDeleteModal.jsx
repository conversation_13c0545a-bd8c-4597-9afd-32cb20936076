import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const BulkDeleteModal = ({ showModal, setShowModal, selectedRequests, onSuccess }) => {
    const [postApi, { isLoading }] = usePostApiMutation();

    const onSubmit = async () => {
        try {
            const response = await postApi({
                end_point: '/admin/requests/bulk-action',
                body: {
                    request_ids: selectedRequests,
                    action: 'delete'
                }
            });

            if (!response.error) {
                setShowModal(false);
                if (onSuccess) onSuccess();
            } else {
                toast.error(response.error?.data?.message || "Failed to delete requests");
            }
        } catch (error) {
            toast.error("An error occurred while deleting requests");
            console.error(error);
        }
    };

    return (
        <Modal
            title="Bulk Delete Requests"
            themeClass="bg-danger-500 dark:bg-danger-700"
            centered={true}
            className="max-w-md"
            activeModal={showModal}
            onClose={() => setShowModal(false)}
        >
            <div className="text-base text-gray-700 dark:text-slate-300">
                <div className="flex items-center mb-6 bg-danger-50 p-4 rounded-lg border border-danger-100">
                    <div className="mr-4">
                        <div className="h-12 w-12 rounded-full bg-danger-100 flex items-center justify-center">
                            <Icon icon="heroicons:trash" className="h-6 w-6 text-danger-600" />
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Confirm Bulk Deletion</h3>
                        <p className="text-sm text-gray-600">
                            Are you sure you want to delete {selectedRequests.length} selected request(s)?
                        </p>
                    </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                        <Icon icon="heroicons:exclamation-triangle" className="h-5 w-5 text-yellow-600 mr-2" />
                        <p className="text-sm text-yellow-800">
                            <strong>Warning:</strong> This action cannot be undone. All selected requests will be permanently deleted.
                        </p>
                    </div>
                </div>

                <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">Requests to be deleted:</p>
                    <div className="max-h-32 overflow-y-auto bg-gray-50 rounded-lg p-3">
                        <p className="text-sm text-gray-600">
                            {selectedRequests.length} request(s) selected for deletion
                        </p>
                    </div>
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                    onClick={() => setShowModal(false)}
                    icon="heroicons:x-mark"
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onSubmit}
                    isLoading={isLoading}
                    icon="heroicons:trash"
                >
                    Delete {selectedRequests.length} Request(s)
                </Button>
            </div>
        </Modal>
    );
};

export default BulkDeleteModal;
