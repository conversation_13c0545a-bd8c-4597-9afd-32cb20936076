import React from "react";
import useFooterType from "@/hooks/useFooterType";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react";

const MobileFooter = ({ className = "custom-class" }) => {
  const [footerType] = useFooterType();

  const footerClassName = () => {
    switch (footerType) {
      case "sticky":
        return "sticky bottom-0 z-[999]";
      case "static":
        return "static";
      case "hidden":
        return "hidden";
      default:
        return "static";
    }
  };

  return (
    <footer
      className={`${className} ${footerClassName()} p-6 bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-300 shadow-md`}
    >
      <div className="container mx-auto">
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 items-center">
          {/* Left Section */}
          <div className="text-center sm:text-left text-sm font-medium">
            <p>
              COPYRIGHT &copy; {new Date().getFullYear()} MARKT ZOOM All Rights Reserved
            </p>
          </div>

          {/* Center Section */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <p className="font-semibold">Contact Us:</p>
            <div className="flex space-x-4">
              <a
                href="https://www.facebook.com/Marktzoombd"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-facebook" className="w-5 h-5" />
                <span>Facebook</span>
              </a>
              <a
                href="tel:09611900205"
                className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-phone" className="w-5 h-5" />
                <span>09611900205</span>
              </a>
              <a
                href="tel:+88028396601"
                className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-phone" className="w-5 h-5" />
                <span>+88 02 8396601</span>
              </a>
            </div>
          </div>

          {/* Right Section */}
          <div className="text-center sm:text-right text-sm font-medium">
            <p>
              Hand-crafted & Made by{' '}
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                className="text-indigo-600 font-semibold hover:text-indigo-800"
              >
                Markt Zoom
              </a>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default MobileFooter;
