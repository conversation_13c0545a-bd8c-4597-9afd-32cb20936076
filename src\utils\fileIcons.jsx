import React from 'react';
import Icon from "@/components/ui/Icon";

export const getFileIcon = (filePath) => {
    if (!filePath) return <Icon icon="heroicons:document" className="w-10 h-10 text-gray-500" />;

    const extension = filePath.split('.').pop().toLowerCase();

    switch (extension) {
        case 'pdf':
            return <Icon icon="heroicons:document-text" className="w-10 h-10 text-red-500" />;
        case 'doc':
        case 'docx':
            return <Icon icon="heroicons:document-text" className="w-10 h-10 text-blue-500" />;
        case 'xls':
        case 'xlsx':
            return <Icon icon="heroicons:table-cells" className="w-10 h-10 text-green-500" />;
        case 'ppt':
        case 'pptx':
            return <Icon icon="heroicons:presentation-chart-bar" className="w-10 h-10 text-orange-500" />;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'webp':
        case 'svg':
            return <Icon icon="heroicons:photo" className="w-10 h-10 text-blue-400" />;
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
            return <Icon icon="heroicons:film" className="w-10 h-10 text-purple-500" />;
        case 'mp3':
        case 'wav':
        case 'ogg':
            return <Icon icon="heroicons:musical-note" className="w-10 h-10 text-yellow-500" />;
        case 'csv':
            return <Icon icon="heroicons:table-cells" className="w-10 h-10 text-teal-500" />;
        case 'zip':
        case 'rar':
        case '7z':
            return <Icon icon="heroicons:archive-box" className="w-10 h-10 text-gray-600" />;
        case 'html':
        case 'css':
        case 'js':
        case 'json':
        case 'xml':
            return <Icon icon="heroicons:code-bracket" className="w-10 h-10 text-slate-700" />;
        default:
            return <Icon icon="heroicons:document" className="w-10 h-10 text-gray-500" />;
    }
};
