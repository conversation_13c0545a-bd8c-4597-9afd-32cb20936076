import React from "react";
import { getStatusColor } from "@/utils/statusHelpers";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";

const OrderInfo = ({ order }) => {
  if (!order) return null;

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6">
      <div className="flex justify-between items-start mb-4">
        <h2 className="text-xl font-bold text-gray-800">Order #{order.id.substring(0, 8)}</h2>
        <div className="flex space-x-2">
          <Badge className={`bg-${getStatusColor(order.order_status)}-100 text-${getStatusColor(order.order_status)}-800 px-3 py-1 rounded-full`}>
            {order.order_status}
          </Badge>
          <Badge className={`bg-${getStatusColor(order.payment_status)}-100 text-${getStatusColor(order.payment_status)}-800 px-3 py-1 rounded-full`}>
            {order.payment_status}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Order Details</h3>
            <div className="mt-2 space-y-2">
              <div className="flex items-center">
                <Icon icon="heroicons-outline:calendar" className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-700">
                  Created: {formatDate(order.created_at)}
                </span>
              </div>
              
              {order.delivery_date && (
                <div className="flex items-center">
                  <Icon icon="heroicons-outline:truck" className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">
                    Delivery Date: {formatDate(order.delivery_date)}
                  </span>
                </div>
              )}
              
              <div className="flex items-center">
                <Icon icon="heroicons-outline:currency-dollar" className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-700">
                  Total Amount: {formatCurrency(order.total_amount)}
                </span>
              </div>
              
              <div className="flex items-center">
                <Icon icon="heroicons-outline:credit-card" className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-700">
                  Payment Method: {order.payment_method || 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {order.tracking_code && (
            <div>
              <h3 className="text-sm font-medium text-gray-500">Shipping Information</h3>
              <div className="mt-2 space-y-2">
                <div className="flex items-center">
                  <Icon icon="heroicons-outline:identification" className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">
                    Tracking Code: {order.tracking_code}
                  </span>
                </div>
              </div>
            </div>
          )}

          {order.cancellation_reason && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 text-red-500">Cancellation Information</h3>
              <div className="mt-2 p-3 bg-red-50 rounded-md">
                <p className="text-sm text-red-700">{order.cancellation_reason}</p>
              </div>
            </div>
          )}

          {order.refund_reason && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 text-orange-500">Refund Information</h3>
              <div className="mt-2 p-3 bg-orange-50 rounded-md">
                <p className="text-sm text-orange-700">{order.refund_reason}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderInfo;
