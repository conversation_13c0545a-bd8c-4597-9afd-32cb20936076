import React, { useEffect, useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import useMobileMenu from "@/hooks/useMobileMenu";
import Submenu from "./Submenu";
import { useSelector } from "react-redux";

const Navmenu = ({ menus }) => {
  const [activeSubmenu, setActiveSubmenu] = useState(null);
  const [activeMultiMenu, setMultiMenu] = useState(null);
  const { user } = useSelector((state) => state.auth);
  const location = useLocation();
  const [mobileMenu, setMobileMenu] = useMobileMenu();
  const dispatch = useDispatch();

  // Helper to check if the current location matches a menu item
  const isLocationMatch = (targetLocation) => {
    return (
      location.pathname === targetLocation ||
      location.pathname.startsWith(`${targetLocation}/`)
    );
  };

  // Toggle submenu (manual user click)
  const toggleSubmenu = (i) => {
    setActiveSubmenu(activeSubmenu === i ? null : i);
  };

  // Toggle multi-level menu (manual user click)
  const toggleMultiMenu = (j) => {
    setMultiMenu(activeMultiMenu === j ? null : j);
  };

  // Automatically detect and set active submenu based on route
  useEffect(() => {
    let submenuIndex = null;
    let multiMenuIndex = null;

    menus.forEach((item, i) => {
      // Check if current route matches this item
      if (item.link && isLocationMatch(item.link)) {
        submenuIndex = i;
      }

      // Check child items
      if (item.child) {
        item.child.forEach((childItem, j) => {
          if (childItem.childlink && isLocationMatch(childItem.childlink)) {
            submenuIndex = i;
          }

          // Check multi-level items
          if (childItem.multi_menu) {
            childItem.multi_menu.forEach((nestedItem) => {
              if (nestedItem.multiLink && isLocationMatch(nestedItem.multiLink)) {
                submenuIndex = i;
                multiMenuIndex = j;
              }
            });
          }
        });
      }
    });

    // Update states only if needed (prevent unnecessary re-renders)
    if (submenuIndex !== activeSubmenu) {
      setActiveSubmenu(submenuIndex);
    }
    if (multiMenuIndex !== activeMultiMenu) {
      setMultiMenu(multiMenuIndex);
    }

    // Close mobile menu on navigation (if open)
    if (mobileMenu) {
      setMobileMenu(false);
    }
  }, [location.pathname]);

  return (
    <ul>
      {menus.map((item, i) => (
        <li
          key={i}
          className={`single-sidebar-menu 
            ${item.child ? "item-has-children" : ""}
            ${activeSubmenu === i ? "open" : ""}
            ${isLocationMatch(item.link) ? "menu-item-active" : ""}`}
        >
          {/* Single menu item (no children) */}
          {!item.child && !item.isHeadr && (
            <NavLink className="menu-link" to={item.link}>
              <span className="menu-icon flex-grow-0">
                <Icon icon={item.icon} />
              </span>
              <div className="text-box flex-grow">{item.title}</div>
              {item.badge && <span className="menu-badge">{item.badge}</span>}
            </NavLink>
          )}

          {/* Menu label (header) */}
          {item.isHeadr && !item.child && (
            <div className="menulabel">{item.title}</div>
          )}

          {/* Parent menu item with children */}
          {item.child && (
            <div
              className={`menu-link ${
                activeSubmenu === i ? "parent_active not-collapsed" : "collapsed"
              }`}
              onClick={() => toggleSubmenu(i)}
            >
              <div className="flex-1 flex items-start">
                <span className="menu-icon">
                  <Icon icon={item.icon} />
                </span>
                <div className="text-box">{item.title}</div>
              </div>
              <div className="flex-0">
                <div
                  className={`menu-arrow transform transition-all duration-300 ${
                    activeSubmenu === i ? "rotate-90" : ""
                  }`}
                >
                  <Icon icon="heroicons-outline:chevron-right" />
                </div>
              </div>
            </div>
          )}

          {/* Render submenu if this item has children */}
          <Submenu
            activeSubmenu={activeSubmenu}
            item={item}
            i={i}
            toggleMultiMenu={toggleMultiMenu}
            activeMultiMenu={activeMultiMenu}
          />
        </li>
      ))}
    </ul>
  );
};

export default Navmenu;