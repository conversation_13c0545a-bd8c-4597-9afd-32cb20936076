import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { toast } from "react-toastify";
import { refreshAccessToken } from "@/utils/tokenRefresh";
import { logOut } from "@/store/api/auth/authSlice";

const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_HOST_URL,
  prepareHeaders: (headers, { getState }) => {
    headers.set("Accept", `Application/json`);
    const token = getState().auth?.token;

    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }

    return headers;
  },
});

function capitalizeWords(str) {
  return str
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

const baseQueryWithReauth = async (args, api, extraOptions) => {
  // Try the initial query
  let result = await baseQuery(args, api, extraOptions);

  // Handle 403 Forbidden errors (token expired but still present)
  if (result?.error?.status === 403) {
    console.log('Token expired. Attempting to refresh...');

    // Try to get a new token
    const newToken = await refreshAccessToken();

    if (newToken) {
      console.log('Token refreshed successfully. Retrying request...');

      // Retry the initial query with new token
      // The token will be picked up automatically from the Redux store
      result = await baseQuery(args, api, extraOptions);
    } else {
      // If refresh token fails, log the user out
      console.log('Token refresh failed. Logging out...');

      // Dispatch logout action to clear state and cookies
      api.dispatch(logOut());

      // Show error message
      toast.error("Your session has expired. Please log in again.");

      // Redirect to login page after a short delay
      setTimeout(() => {
        window.location.href = "/";
      }, 1000);
    }
  }

  // Handle 401 Unauthorized errors (no valid token)
  // else if (result?.error?.status === 401) {
  //   console.log('Unauthorized access. Logging out...');

  //   api.dispatch(logOut());

  //   toast.error("Session expired. Please login again");

  //   setTimeout(() => {
  //     window.location.href = "/";
  //   }, 1000);

  //   return result;
  // }

  // Handle 400 Bad Request errors
  else if (result?.error?.status === 400) {
    toast.error(result?.error?.data?.message || "Bad request");
  }

  // Handle other errors
  else if (result?.error) {
    const errorMessage = result.error?.data?.message || "Something went wrong. Please try again";
    if (args.showToast && result.error.status !== 422) {
      toast.error(errorMessage);
    }
  }
  // Handle success
  else if (result?.data) {
    if (api.type === "mutation" && args.showToast) {
      toast.success(capitalizeWords(result.data?.message) || "Operation successful");
    }
  }

  return result;
};

export const apiSlice = createApi({
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Admin"],
  reducerPath: "apiSliceAdmin",

  endpoints: (builder) => ({

  }),
});
