import React from "react";
import { getStatusColor } from "@/utils/statusHelpers";
import Badge from "@/components/ui/Badge";
import Icon from "@/components/ui/Icon";

const PaymentInfo = ({ payments }) => {
  if (!payments || payments.length === 0) return null;

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get payment method icon
  const getPaymentMethodIcon = (method) => {
    if (!method) return "heroicons-outline:credit-card";
    
    switch (method.toLowerCase()) {
      case 'credit_card':
        return "heroicons-outline:credit-card";
      case 'paypal':
        return "heroicons-outline:currency-dollar";
      case 'bank_transfer':
        return "heroicons-outline:building-library";
      default:
        return "heroicons-outline:credit-card";
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Payment Information</h2>
      
      <div className="space-y-6">
        {payments.map((payment) => (
          <div key={payment.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Icon 
                    icon={getPaymentMethodIcon(payment.payment_method)} 
                    className="h-5 w-5 text-blue-600" 
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {payment.payment_method ? payment.payment_method.replace('_', ' ').toUpperCase() : 'N/A'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {payment.transaction_id ? `Transaction ID: ${payment.transaction_id}` : 'No transaction ID'}
                  </p>
                </div>
              </div>
              <Badge className={`bg-${getStatusColor(payment.payment_status)}-100 text-${getStatusColor(payment.payment_status)}-800 px-2 py-1 rounded-full text-xs`}>
                {payment.payment_status}
              </Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-2">
              <div>
                <p className="text-xs text-gray-500">Amount</p>
                <p className="text-sm font-medium text-gray-900">{formatCurrency(payment.amount)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Date</p>
                <p className="text-sm font-medium text-gray-900">{formatDate(payment.created_at)}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PaymentInfo;
