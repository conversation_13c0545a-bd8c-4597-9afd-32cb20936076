import React, { useState, useEffect, useRef } from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray } from 'formik';
import * as Yup from 'yup';
// import KeywordsInput from './../KeywordsInput';
import { usePostApiMutation, useUpdateApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import { useParams } from 'react-router-dom';
import { skipToken } from "@reduxjs/toolkit/query";
import { Icon } from '@iconify/react';
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Textarea from "@/components/ui/Textarea";
import { FiChevronLeft, FiSave, FiX, FiAlertCircle, FiInfo, FiPlus, FiTrash2 } from 'react-icons/fi';
import Card from '@/components/ui/Card';
import SectionHeader from '@/components/ui/SectionHeader';
import Badge from '@/components/ui/Badge';

const CreateSubCategoryPage = ({ setIsCreatingSubCategory, subcategroyid = null }) => {
  const { id } = useParams();
  const formikRef = useRef();

  const { data: categoryData } = useGetApiQuery(
    subcategroyid ? `admin/subcategories/${subcategroyid}` : skipToken
  );

  const [postApi, { isLoading: isPostLoading }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdateLoading }] = useUpdateApiMutation();

  const [imagePreview, setImagePreview] = useState(null);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);

  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    description: Yup.string(),
    // color: Yup.string().matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color'),
    image: subcategroyid
      ? Yup.mixed()
      : Yup.mixed()
          .required('Image is required')
          .test('is-file', 'Image is required', value => {
            return value instanceof File || value === null || value === undefined;
          }),

    // seo_title: Yup.string(),
    // seo_description: Yup.string(),
    // seo_keywords: Yup.string(),
    translations: Yup.array().of(
      Yup.object().shape({
        language: Yup.string().required(),
        title: Yup.string().required('Translation title is required'),
        description: Yup.string(),
        // seo_title: Yup.string(),
        // seo_description: Yup.string(),
        // seo_keywords: Yup.string(),
      })
    ),
    form_fields: Yup.array().of(
      Yup.object().shape({
        label_name: Yup.string().required('Label name is required'),
        input_type: Yup.string().required('Input type is required'),
        label_subtitle: Yup.string().nullable(),
        is_required: Yup.boolean().default(false),
        options: Yup.array().when('input_type', {
          is: (type) => ['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(type),
          then: () => Yup.array().min(1, 'At least one option is required for this input type'),
          otherwise: () => Yup.array()
        })
      })
    )
  });

  const getInitialValues = () => {
    if (!subcategroyid || !categoryData?.data) {
      return {
        title: '',
        description: '',
        // color: '#FFFFFF',
        image: null,
        // seo_title: '',
        // seo_description: '',
        // seo_keywords: '',
        translations: [],
        form_fields: [],
      };
    }

    const category = categoryData.data;
    return {
      title: category.title || '',
      description: category.description || '',
      // color: category.color || '#ffffff',
      image: category.image || null,
      // seo_title: category.seo_title || '',
      // seo_description: category.seo_description || '',
      // seo_keywords: category.seo_keywords || '',
      translations: category.translations || [],
      form_fields: category.form_fields || [],
    };
  };

  useEffect(() => {
    if (categoryData?.data) {
      if (categoryData.data.image) {
        setImagePreview(import.meta.env.VITE_ASSET_HOST_URL + categoryData.data.image);
      }
    }
  }, [categoryData]);

  const handleSubmit = async (values, { setErrors, resetForm }) => {
    try {
      setFormSubmitting(true);
      const formData = new FormData();
      formData.append('title', values.title);
      formData.append('description', values.description);
      // formData.append('color', values.color);

      if (values.image && values.image instanceof File) formData.append('image', values.image);
      // formData.append('seo_title', values.seo_title);
      // formData.append('seo_description', values.seo_description);
      // formData.append('seo_keywords', values.seo_keywords);

      values.translations.length > 0 && formData.append('translations', JSON.stringify(values.translations));
      values.form_fields.length > 0 && formData.append('form_fields', JSON.stringify(values.form_fields));

      let response = null;
      if (subcategroyid) {
        formData.append('_method', 'PUT');
        response = await updateApi({
          end_point: '/admin/subcategories/' + subcategroyid,
          body: formData
        });
      } else {
        response = await postApi({
          end_point: '/admin/categories/' + id + '/subcategories',
          body: formData
        });
      }

      if (response.error) {
        if (response.error.data && response.error.data.errors) {
          setErrors(response.error.data.errors);
        }
      } else {
        resetForm();
        setIsCreatingSubCategory(false);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setFormSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => setIsCreatingSubCategory(false)}
            className="mr-3 p-1 rounded-md hover:bg-gray-100 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <FiChevronLeft className="w-5 h-5" />
          </button>
          <div>
            <h2 className="text-lg font-semibold text-gray-800">
              {subcategroyid ? 'Edit Subcategory' : 'Create New Subcategory'}
            </h2>
            <p className="text-xs text-gray-500 mt-1">
              {subcategroyid ? 'Update existing subcategory details' : 'Add a new subcategory to your catalog'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsCreatingSubCategory(false)}
            className="h-9 px-4 flex items-center"
          >
            <FiX className="mr-2 w-4 h-4" /> Cancel
          </Button>
          <Button
            type="button"
            isLoading={formSubmitting || isPostLoading || isUpdateLoading}
            variant="primary"
            className="h-9 px-4 flex items-center"
            onClick={() => {
              if (formSubmitting) return;
              setSubmitAttempted(true);
              const touchedFields = {};
              Object.keys(getInitialValues()).forEach(key => {
                touchedFields[key] = true;
              });
              formikRef.current.setTouched(touchedFields);

              if (!subcategroyid && !formikRef.current.values.image) {
                formikRef.current.setFieldError('image', 'Image is required');
              }

              formikRef.current.validateForm().then(errors => {
                if (Object.keys(errors).length === 0) {
                  formikRef.current.handleSubmit();
                }
              });
            }}
          >
            <FiSave className="mr-2 w-4 h-4" /> {subcategroyid ? 'Update' : 'Save'} Subcategory
          </Button>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-6">
        <Formik
          innerRef={formikRef}
          initialValues={getInitialValues()}
          enableReinitialize={true}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnBlur
          validateOnChange
          validateOnMount
        >
          {({ values, setFieldValue, handleBlur, touched, errors, setTouched, setErrors, setFieldError, validateForm }) => (
            console.log(errors),
            <Form className="space-y-6">
              {/* Error Summary */}
              {submitAttempted && Object.keys(errors).length > 0 && (
                <Card className="border-red-200 bg-red-50">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <FiAlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">There were errors with your submission</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <ul className="list-disc pl-5 space-y-1">
                          {Object.entries(errors).map(([field, error]) => (
                            typeof error === 'string' ? (
                              <li key={field}>{error}</li>
                            ) : null
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-6">
                  {/* Basic Information Card */}
                  <Card>
                    <SectionHeader
                      title="Basic Information"
                      description="Core details about the subcategory"
                    />
                    <div className="space-y-4">
                      <InputField
                        name="title"
                        label="Title"
                        type="text"
                        required
                        placeholder="e.g., Summer Collection"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.title}
                      />

                      <Textarea
                        name="description"
                        label="Description"
                        rows={3}
                        placeholder="Brief description of the subcategory"
                        value={values.description}
                        onChange={(e) => setFieldValue('description', e.target.value)}
                        onBlur={handleBlur}
                        error={submitAttempted && errors.description}
                      />

                      {/* <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
                        <div className="flex items-center gap-3">
                          <Field
                            name="color"
                            type="color"
                            className="h-10 w-10 cursor-pointer rounded-md border border-gray-300 p-0"
                          />
                          <div className="flex-1">
                            <InputField
                              name="color"
                              type="text"
                              placeholder="#3b82f6"
                              onBlur={handleBlur}
                              error={submitAttempted && errors.color}
                              required
                            />
                          </div>
                        </div>
                      </div> */}
                    </div>
                  </Card>

                  {/* Form Fields Section */}
                  <Card>
                    <SectionHeader
                      title="Form Fields"
                      description="Custom fields for this subcategory"
                      badge={<Badge variant="info">{values.form_fields.length} fields</Badge>}
                    />
                    <div className="space-y-4">
                      <FieldArray name="form_fields">
                        {({ push, remove }) => (
                          <div className="space-y-4">
                            {values.form_fields.map((field, index) => (
                              <div key={index} className="p-4 border rounded-lg bg-gray-50 relative group">
                                <div className="absolute top-3 right-3 flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <button
                                    type="button"
                                    onClick={() => remove(index)}
                                    className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50"
                                    title="Remove field"
                                  >
                                    <FiTrash2 className="w-4 h-4" />
                                  </button>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <InputField
                                    name={`form_fields.${index}.label_name`}
                                    label="Label Name"
                                    type="text"
                                    required
                                    placeholder="e.g., Size, Color"
                                    error={submitAttempted && errors.form_fields?.[index]?.label_name}
                                  />

                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-3">Input Type *</label>
                                    <Field
                                      as="select"
                                      name={`form_fields.${index}.input_type`}
                                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                                        submitAttempted && errors.form_fields?.[index]?.input_type ? 'border-red-500' : 'border-gray-300'
                                      }`}
                                    >
                                      <option value="">Select Type</option>
                                      <option value="TEXT">Text</option>
                                      <option value="TEXTAREA">Text Area</option>
                                      <option value="NUMBER">Number</option>
                                      <option value="EMAIL">Email</option>
                                      <option value="SELECT">Dropdown</option>
                                      <option value="CHECKBOX">Checkbox</option>
                                      <option value="RADIO">Radio</option>
                                      <option value="MULTISELECT">Multi Select</option>
                                      <option value="FILE">File Upload</option>
                                      <option value="DATE">Date</option>
                                    </Field>
                                    {submitAttempted && errors.form_fields?.[index]?.input_type && (
                                      <p className="mt-1 text-sm text-red-600">{errors.form_fields[index].input_type}</p>
                                    )}
                                  </div>

                                  <InputField
                                    name={`form_fields.${index}.label_subtitle`}
                                    label="Help Text"
                                    type="text"
                                    placeholder="Optional helper text"
                                  />

                                  <div className="flex items-center pt-6">
                                    <label className="flex items-center cursor-pointer">
                                      <Field
                                        type="checkbox"
                                        name={`form_fields.${index}.is_required`}
                                        className="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                                      />
                                      <span className="ml-2 text-sm text-gray-700">Required Field</span>
                                    </label>
                                  </div>
                                </div>

                                {/* Options for field types that need them */}
                                {['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(field.input_type) && (
                                  <div className="mt-4 p-3 border border-dashed border-gray-300 rounded-lg bg-white">
                                    <div className="flex justify-between items-center mb-2">
                                      <label className="block text-sm font-medium text-gray-700">Options</label>
                                      {submitAttempted && errors.form_fields?.[index]?.options && (
                                        <span className="text-sm text-red-600">At least one option required</span>
                                      )}
                                    </div>
                                    <FieldArray name={`form_fields.${index}.options`}>
                                      {({ push: pushOption, remove: removeOption }) => (
                                        <div className="space-y-2">
                                          {field.options?.map((_, optionIndex) => (
                                            <div key={optionIndex} className="flex items-center gap-2">
                                              <Field
                                                name={`form_fields.${index}.options.${optionIndex}`}
                                                type="text"
                                                className="flex-1 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 border-gray-300"
                                                placeholder={`Option ${optionIndex + 1}`}
                                              />
                                              <button
                                                type="button"
                                                onClick={() => removeOption(optionIndex)}
                                                className="text-gray-500 hover:text-red-600 p-1 rounded hover:bg-gray-100"
                                              >
                                                <FiX className="w-4 h-4" />
                                              </button>
                                            </div>
                                          ))}
                                          <button
                                            type="button"
                                            onClick={() => pushOption('')}
                                            className="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                                          >
                                            <FiPlus className="w-3 h-3 mr-1" />
                                            Add Option
                                          </button>
                                        </div>
                                      )}
                                    </FieldArray>
                                  </div>
                                )}


                              </div>
                            ))}

                            <button
                              type="button"
                              onClick={() => push({
                                label_name: '',
                                input_type: '',
                                label_subtitle: '',
                                is_required: false,
                                options: []
                              })}
                              className="mt-2 inline-flex items-center justify-center w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-blue-600 hover:text-blue-800 hover:border-blue-300 bg-gray-50 hover:bg-blue-50 transition-colors"
                            >
                              <FiPlus className="w-4 h-4 mr-2" />
                              Add Form Field
                            </button>
                          </div>
                        )}
                      </FieldArray>
                    </div>
                  </Card>
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  {/* Media Card */}
                  <Card>
                    <SectionHeader
                      title="Media"
                      description="Images for the subcategory"
                    />
                    <div className="space-y-4">
                      <Fileinput
                        title="Main Image"
                        name="image"
                        label="Select Image"
                        placeholder="Recommended size: 800x600px"
                        accept="image/*"
                        preview
                        onChange={(e) => {
                          const file = e.target.files[0];
                          if (file) {
                            setFieldValue('image', file);
                            setFieldError('image', undefined);
                            setErrors({ ...errors, image: undefined });
                            setImagePreview(URL.createObjectURL(file));
                          }
                        }}
                        fieldName="image"
                        previewImage={imagePreview}
                        setPreviewImage={setImagePreview}
                        selectedFile={values.image}
                        required={!subcategroyid}
                        error={submitAttempted && errors.image}
                        onBlur={() => {
                          setTouched({ ...touched, image: true });
                          if (values.image instanceof File) {
                            setFieldError('image', undefined);
                            setErrors({ ...errors, image: undefined });
                          }
                          handleBlur({ target: { name: 'image' } });
                        }}
                      />
                    </div>
                  </Card>

                  {/* SEO Settings Card - Moved to dedicated modal */}
                  {/* <Card>
                    <SectionHeader
                      title="SEO Settings"
                      description="Optimize for search engines"
                    />
                    <div className="space-y-4">
                      <InputField
                        name="seo_title"
                        label="SEO Title"
                        type="text"
                        placeholder="Keep under 60 characters"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.seo_title}
                      />

                      <Textarea
                        name="seo_description"
                        label="SEO Description"
                        rows={2}
                        placeholder="Keep under 160 characters"
                        value={values.seo_description}
                        onChange={(e) => setFieldValue('seo_description', e.target.value)}
                        onBlur={handleBlur}
                        error={submitAttempted && errors.seo_description}
                      />

                      <KeywordsInput name="seo_keywords" />
                    </div>
                  </Card> */}

                  {/* Translations Card */}
                  {values?.translations.length > 0 && (
                    <Card>
                      <SectionHeader
                        title="Translations"
                        description="Localized content for different languages"
                        badge={<Badge variant="info">{values.translations.length} languages</Badge>}
                      />
                      <div className="space-y-4">
                        {values.translations.map((translation, index) => (
                          <div key={index} className="p-4 bg-gray-50 rounded-md border border-gray-200">
                            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                                {translation.language.toUpperCase()}
                              </span>
                              {translation.language === 'bn' ? 'Bengali' : 'Arabic'} Translation
                            </h4>

                            <div className="space-y-4">
                              <InputField
                                name={`translations.${index}.title`}
                                label="Title"
                                type="text"
                                required
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.title}
                              />

                              <Textarea
                                name={`translations.${index}.description`}
                                label="Description"
                                rows={2}
                                value={translation.description || ''}
                                onChange={(e) => setFieldValue(`translations.${index}.description`, e.target.value)}
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.description}
                              />

                              {/* <InputField
                                name={`translations.${index}.seo_title`}
                                label="SEO Title"
                                type="text"
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.seo_title}
                              />

                              <InputField
                                name={`translations.${index}.seo_description`}
                                label="SEO Description"
                                type="text"
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.seo_description}
                              />

                              <InputField
                                name={`translations.${index}.seo_keywords`}
                                label="SEO Keywords"
                                type="text"
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.seo_keywords}
                              /> */}
                            </div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  )}
                </div>
              </div>

              <div className="pt-4 flex justify-end gap-3 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreatingSubCategory(false)}
                  className="h-10 px-6"
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  isLoading={formSubmitting || isPostLoading || isUpdateLoading}
                  variant="primary"
                  className="h-10 px-6"
                  onClick={async () => {
                    if (formSubmitting) return;
                    setSubmitAttempted(true);

                    const touchedFields = {};
                    Object.keys(values).forEach(key => {
                      touchedFields[key] = true;
                    });
                    setTouched(touchedFields);

                    const errors = await validateForm();
                    if (Object.keys(errors).length === 0) {
                      formikRef.current.submitForm();
                    }
                  }}
                >
                  {subcategroyid ? 'Update Subcategory' : 'Create Subcategory'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateSubCategoryPage;