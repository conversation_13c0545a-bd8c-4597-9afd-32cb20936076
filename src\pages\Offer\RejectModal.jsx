import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import Textarea from "@/components/ui/Textarea";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const RejectModal = ({ showRejectModal, setShowRejectModal, data, onSuccess }) => {
    const [rejectionReason, setRejectionReason] = useState("");
    const [updateApi, { isLoading }] = useUpdateApiMutation();

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    const onSubmit = async () => {
        if (!rejectionReason.trim()) {
            toast.error("Please provide a rejection reason");
            return;
        }

        try {
            const response = await updateApi({
                end_point: '/admin/offers/' + data?.id + '/status',
                body: {
                    status: 'Rejected',
                    reason: rejectionReason.trim() || "This offer doesn't meet our requirements"
                }
            });

            if (!response.error) {
                setShowRejectModal(false);
                // Call the onSuccess callback to refresh the data
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess();
                }
            } else {
                toast.error(response.error?.data?.message || "Failed to reject offer");
            }
        } catch (error) {
            toast.error("An error occurred while rejecting the offer");
            console.error(error);
        }
    };

    return (
        <Modal
            title="Reject Offer"
            themeClass="bg-danger-500 dark:bg-danger-700"
            centered={true}
            className="max-w-md"
            activeModal={showRejectModal}
            onClose={() => setShowRejectModal(false)}
        >
            <div className="text-base text-gray-700 dark:text-slate-300">
                <div className="flex items-center mb-6 bg-danger-50 p-4 rounded-lg border border-danger-100">
                    <div className="mr-4">
                        <div className="h-12 w-12 rounded-full bg-danger-100 flex items-center justify-center">
                            <Icon icon="heroicons:x-mark" className="h-6 w-6 text-danger-600" />
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Confirm Rejection</h3>
                        <p className="text-sm text-gray-600">Are you sure you want to reject this offer?</p>
                    </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-100">
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Offer Details</h4>
                    <div className="space-y-3">
                        {data?.title && (
                            <div className="flex justify-between text-sm">
                                <span className="font-medium text-gray-600">Title:</span>
                                <span className="text-gray-800">{data.title}</span>
                            </div>
                        )}
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Price:</span>
                            <span className="text-gray-800 font-semibold">{formatCurrency(data?.price || 0)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Delivery Time:</span>
                            <span className="text-gray-800">{data?.delivery_time} days</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Seller:</span>
                            <span className="text-gray-800">
                                {data?.seller?.first_name} {data?.seller?.last_name}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Rejection Reason <span className="text-danger-500">*</span>
                    </label>
                    <Textarea
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        placeholder="Please provide a reason for rejection..."
                        className="w-full"
                        rows={3}
                        required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                        This reason will be visible to the seller and helps them understand why their offer was rejected.
                    </p>
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                    onClick={() => setShowRejectModal(false)}
                    icon="heroicons:arrow-left"
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onSubmit}
                    isLoading={isLoading}
                    icon="heroicons:x-mark"
                    disabled={!rejectionReason.trim()}
                >
                    Reject Offer
                </Button>
            </div>
        </Modal>
    );
};

export default RejectModal;
