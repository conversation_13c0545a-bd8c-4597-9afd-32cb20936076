import React, { useState, useRef } from "react";
import { useParams } from "react-router-dom";
import { useGetApiQuery, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";
import Loading from "@/components/Loading";
import { format } from "date-fns";
import { DEFAULT_USER_ICON } from "@/config";
import { toast } from "react-toastify";

// Import components
import UserHeader from "./components/UserHeader";
import UserProfileSidebar from "./components/UserProfileSidebar";
import StatusCards from "./components/StatusCards";
import PersonalInformationCard from "./components/PersonalInformationCard";
import AccountActionsCard from "./components/AccountActionsCard";
import BusinessInformationCard from "./components/BusinessInformationCard";

// Import modals
import PasswordUpdateModal from "./components/PasswordUpdateModal";
import RoleChangeModal from "./components/RoleChangeModal";

const UserDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [avatarError, setAvatarError] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);

  // Modal states
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);

  // API hooks
  const [updateApi] = useUpdateApiMutation();

  // Fetch user data
  const { data: response, isLoading, isError, refetch } = useGetApiQuery(`admin/users/${id}`);
  const user = response?.data || {};

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'PPP');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Handle avatar loading error
  const handleAvatarError = () => {
    setAvatarError(true);
  };

  // Get avatar URL
  const getAvatarUrl = () => {
    if (avatarError || !user.profile_picture_url) {
      return DEFAULT_USER_ICON;
    }

    if (user.profile_picture_url.startsWith('http')) {
      return user.profile_picture_url;
    }

    return `${import.meta.env.VITE_ASSET_HOST_URL}${user.profile_picture_url}`;
  };

  // Handle avatar upload
  const handleAvatarUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please select a valid image file (JPEG, PNG, GIF, WEBP)');
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast.error('Image size should be less than 5MB');
      return;
    }

    try {
      setIsUploading(true);
      setAvatarError(false);

      const formData = new FormData();
      formData.append('avatar', file);
      formData.append('_method', 'PUT');

      const response = await updateApi({
        end_point: `admin/users/${id}`,
        body: formData,
        notoast: true
      });

      if (response.error) {
        toast.error(response.error.data?.message || 'Failed to update profile picture');
        setAvatarError(true);
      } else {
        toast.success('Profile picture updated successfully');
        refetch(); // Refresh user data
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('An error occurred while uploading the profile picture');
      setAvatarError(true);
    } finally {
      setIsUploading(false);
    }
  };

  // Get status badge color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'suspended':
        return 'danger';
      default:
        return 'gray';
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (isError) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h2 className="text-xl font-semibold text-red-700">Error Loading User</h2>
        <p className="mt-2 text-red-600">There was an error loading the user details. Please try again later.</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => navigate(-1)}
        >
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto px-4 md:px-6 py-4 md:py-6 bg-gray-50 min-h-screen">
      {/* Header with breadcrumb and actions */}
      <UserHeader user={user} />

      {/* Main content */}
      <div className="grid grid-cols-12 gap-4 md:gap-6">
        {/* Left sidebar - User avatar and quick actions */}
        <div className="col-span-12 md:col-span-4 lg:col-span-3 order-2 md:order-1">
          <UserProfileSidebar
            user={user}
            getAvatarUrl={getAvatarUrl}
            handleAvatarError={handleAvatarError}
            isUploading={isUploading}
            fileInputRef={fileInputRef}
            handleAvatarUpload={handleAvatarUpload}
            getStatusColor={getStatusColor}
            setShowPasswordModal={setShowPasswordModal}
            setShowRoleModal={setShowRoleModal}
            formatDate={formatDate}
          />
        </div>

        {/* Main content area */}
        <div className="col-span-12 md:col-span-8 lg:col-span-9 space-y-4 md:space-y-6 order-1 md:order-2">
          {/* Status cards row */}
          <StatusCards user={user} />

          {/* Personal Information */}
          <PersonalInformationCard user={user} formatDate={formatDate} />

          {/* Business Information - Only show if user has business information */}
          {user.business_informations && user.business_informations.length > 0 && (
            <BusinessInformationCard user={user} />
          )}

          {/* Actions Card */}
          <AccountActionsCard user={user} />
        </div>
      </div>

      {/* Modals */}
      <PasswordUpdateModal
        showModal={showPasswordModal}
        setShowModal={setShowPasswordModal}
        userId={id}
      />

      <RoleChangeModal
        showModal={showRoleModal}
        setShowModal={setShowRoleModal}
        userId={id}
        currentRole={user.role}
      />
    </div>
  );
};

export default UserDetailsPage;