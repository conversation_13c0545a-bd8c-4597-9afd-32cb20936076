import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useGetApiQuery, usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast } from 'react-toastify';
import Loading from "@/components/Loading";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import Card from "@/components/ui/Card";
// Note: Using native HTML elements instead of Formik-based components

// Validation schema
const schema = yup.object({
  title: yup.string().required("Title is required").min(3, "Title must be at least 3 characters").max(200, "Title must not exceed 200 characters"),
  content: yup.string().required("Content is required").min(10, "Content must be at least 10 characters"),
  excerpt: yup.string().max(500, "Excerpt must not exceed 500 characters"),
  featured_image: yup.string().url("Featured image must be a valid URL"),
  meta_title: yup.string().max(60, "Meta title must not exceed 60 characters"),
  meta_description: yup.string().max(160, "Meta description must not exceed 160 characters"),
  meta_keywords: yup.string().max(255, "Meta keywords must not exceed 255 characters"),
  status: yup.string().oneOf(['draft', 'published', 'scheduled', 'archived'], "Invalid status"),
  is_featured: yup.boolean(),
  category_id: yup.string(),
  tags: yup.string(),
  reading_time: yup.number().positive("Reading time must be positive").integer("Reading time must be an integer"),
});

const CreateOrUpdateBlogPost = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = Boolean(id);
  
  const [createPost, { isLoading: isCreating }] = usePostApiMutation();
  const [updatePost, { isLoading: isUpdating }] = useUpdateApiMutation();
  
  // Fetch post data for editing
  const { 
    data: postData, 
    isLoading: isLoadingPost 
  } = useGetApiQuery(isEdit ? `/admin/blog/posts/${id}` : null, {
    skip: !isEdit
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      title: "",
      content: "",
      excerpt: "",
      featured_image: "",
      meta_title: "",
      meta_description: "",
      meta_keywords: "",
      status: "draft",
      is_featured: false,
      category_id: "",
      tags: "",
      reading_time: ""
    }
  });

  // Status options
  const statusOptions = [
    { value: "draft", label: "Draft" },
    { value: "published", label: "Published" },
    { value: "scheduled", label: "Scheduled" },
    { value: "archived", label: "Archived" }
  ];

  // Load post data for editing
  useEffect(() => {
    if (isEdit && postData?.data) {
      const post = postData.data;
      reset({
        title: post.title || "",
        content: post.content || "",
        excerpt: post.excerpt || "",
        featured_image: post.featured_image || "",
        meta_title: post.meta_title || "",
        meta_description: post.meta_description || "",
        meta_keywords: post.meta_keywords || "",
        status: post.status || "draft",
        is_featured: post.is_featured || false,
        category_id: post.category_id || "",
        tags: post.tags || "",
        reading_time: post.reading_time || ""
      });
    }
  }, [isEdit, postData, reset]);

  const onSubmit = async (data) => {
    try {
      const payload = {
        ...data,
        reading_time: data.reading_time ? parseInt(data.reading_time) : null,
        is_featured: Boolean(data.is_featured)
      };

      let response;
      if (isEdit) {
        response = await updatePost({
          end_point: `/admin/blog/posts/${id}`,
          body: payload
        });
      } else {
        response = await createPost({
          end_point: '/admin/blog/posts',
          body: payload
        });
      }

      if (response.error) {
        console.error('Error saving blog post:', response.error);
        toast.error(response.error.data?.message || `Failed to ${isEdit ? 'update' : 'create'} blog post`);
      } else {
        toast.success(`Blog post ${isEdit ? 'updated' : 'created'} successfully!`);
        navigate('/blog/posts');
      }
    } catch (error) {
      console.error('Error saving blog post:', error);
      toast.error(`An error occurred while ${isEdit ? 'updating' : 'creating'} the blog post`);
    }
  };

  if (isEdit && isLoadingPost) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  const isLoading = isCreating || isUpdating;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            {isEdit ? 'Edit Blog Post' : 'Create New Blog Post'}
          </h1>
          <p className="text-sm text-gray-600 mt-1">
            {isEdit ? 'Update blog post information' : 'Create a new blog post'}
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate('/blog/posts')}
          className="h-9 px-4 flex items-center"
        >
          <Icon icon="heroicons:arrow-left" className="mr-2 w-4 h-4" /> Back to Posts
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Post Content</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="Enter post title"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.title ? 'border-red-500' : ''
                      }`}
                      {...register("title")}
                    />
                    {errors.title && (
                      <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Excerpt
                    </label>
                    <textarea
                      rows={3}
                      placeholder="Brief description of the post (optional)"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.excerpt ? 'border-red-500' : ''
                      }`}
                      {...register("excerpt")}
                    />
                    {errors.excerpt && (
                      <p className="text-red-500 text-sm mt-1">{errors.excerpt.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Content <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      rows={12}
                      placeholder="Write your blog post content here..."
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.content ? 'border-red-500' : ''
                      }`}
                      {...register("content")}
                    />
                    {errors.content && (
                      <p className="text-red-500 text-sm mt-1">{errors.content.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Featured Image URL
                    </label>
                    <input
                      type="url"
                      placeholder="https://example.com/image.jpg"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.featured_image ? 'border-red-500' : ''
                      }`}
                      {...register("featured_image")}
                    />
                    {errors.featured_image && (
                      <p className="text-red-500 text-sm mt-1">{errors.featured_image.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tags
                    </label>
                    <input
                      type="text"
                      placeholder="tag1, tag2, tag3"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.tags ? 'border-red-500' : ''
                      }`}
                      {...register("tags")}
                    />
                    {errors.tags && (
                      <p className="text-red-500 text-sm mt-1">{errors.tags.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </Card>

            {/* SEO Settings */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">SEO Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Title
                    </label>
                    <input
                      type="text"
                      placeholder="SEO title (max 60 characters)"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.meta_title ? 'border-red-500' : ''
                      }`}
                      {...register("meta_title")}
                    />
                    {errors.meta_title && (
                      <p className="text-red-500 text-sm mt-1">{errors.meta_title.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Description
                    </label>
                    <textarea
                      rows={3}
                      placeholder="SEO description (max 160 characters)"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.meta_description ? 'border-red-500' : ''
                      }`}
                      {...register("meta_description")}
                    />
                    {errors.meta_description && (
                      <p className="text-red-500 text-sm mt-1">{errors.meta_description.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Keywords
                    </label>
                    <input
                      type="text"
                      placeholder="keyword1, keyword2, keyword3"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.meta_keywords ? 'border-red-500' : ''
                      }`}
                      {...register("meta_keywords")}
                    />
                    {errors.meta_keywords && (
                      <p className="text-red-500 text-sm mt-1">{errors.meta_keywords.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Post Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    <select
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.status ? 'border-red-500' : ''
                      }`}
                      {...register("status")}
                    >
                      {statusOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.status && (
                      <p className="text-red-500 text-sm mt-1">{errors.status.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
                        {...register("is_featured")}
                      />
                      <span className="ml-2 text-sm text-gray-700">Featured Post</span>
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reading Time (minutes)
                    </label>
                    <input
                      type="number"
                      placeholder="5"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                        errors.reading_time ? 'border-red-500' : ''
                      }`}
                      {...register("reading_time")}
                    />
                    {errors.reading_time && (
                      <p className="text-red-500 text-sm mt-1">{errors.reading_time.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </Card>

            {/* Actions */}
            <Card>
              <div className="p-6">
                <div className="space-y-3">
                  <Button
                    type="submit"
                    variant="primary"
                    className="w-full"
                    disabled={isLoading}
                    isLoading={isLoading}
                  >
                    {isLoading 
                      ? (isEdit ? 'Updating...' : 'Creating...') 
                      : (isEdit ? 'Update Post' : 'Create Post')
                    }
                  </Button>
                  
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => navigate('/blog/posts')}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateOrUpdateBlogPost;
