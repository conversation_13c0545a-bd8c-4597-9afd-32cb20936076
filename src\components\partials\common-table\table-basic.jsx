import React, { useState } from "react";
import Card from "@/components/ui/Card";
import GlobalFilter from "./GlobalFilter";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import Pagination from "./pagination";
import Loading from "@/components/Loading";
import { Link } from "react-router-dom";

const BasicTablePage = ({
  tableHeaderExtra = null,
  title,
  CreateUrl,
  actions = [],
  columns,
  changePage,
  data,
  filter = true,
  setFilter,
  currentPage,
  totalPages,
  loading,
}) => {

  const handlePageChange = (value) => {
    changePage(`?page=${value}`);
  };


  return (
    <div className="">
      <Card noborder>
        <div className="md:flex justify-between items-center mb-6">
          <h4 className="card-title break-words overflow-hidden">{title}</h4>
          <div className="flex gap-2">
            {tableHeaderExtra}

            {filter && <GlobalFilter filter={filter} setFilter={setFilter} /> }
            {CreateUrl && (
              <Link
                className="flex gap-2 items-center font-bold btn btn-primary btn-sm"
                to={CreateUrl}
              >
                <Icon name="plus" icon="heroicons-outline:plus" /> Add New
              </Link>
            )}
          </div>
        </div>
        {loading ? (
          <Loading />
        ) : data?.length > 0 ? (
          <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
            <thead className="bg-slate-200 dark:bg-slate-700">
              <tr>
                {columns.map((column, i) => (
                  <th key={i} scope="col" className=" table-th ">
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
              {data?.map((row, dataIndex) => (
                <tr
                  key={dataIndex}
                  className="hover:bg-slate-200 dark:hover:bg-slate-700"
                >
                  {columns.map(
                    (column, index) =>
                      column.field && (
                        <td key={index} className="table-td">
                          {row[column.field]}
                        </td>
                      )
                  )}
                  {actions.length > 0 && (
                    <td className="table-td ">
                      <Dropdown
                        classMenuItems="right-0 top-[110%] flex"
                        label={
                          <span className="text-xl text-center block px-2">
                            <Icon icon="heroicons-outline:dots-vertical" />
                          </span>
                        }
                      >
                        <div className="divide-y divide-slate-100 dark:divide-slate-800">
                          {actions
                            .filter((item) => !item.hidden)
                            .map((item, i) => (
                              <Menu.Item key={i}>
                                <div
                                  className={`
                          border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm cursor-pointer
                          first:rounded-t last:rounded-b flex gap-2 items-center rtl:space-x-reverse whitespace-nowrap hover:bg-gray-50`}
                                  onClick={() => item.onClick(dataIndex)}
                                >
                                  <span className="text-base">
                                    <Icon icon={item?.icon} />
                                  </span>
                                  <span>{item.name} </span>
                                </div>
                              </Menu.Item>
                            ))}
                        </div>
                      </Dropdown>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          "No data found"
        )}
        {totalPages > 0 && (
          <div className="p-4 flex justify-end">
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </div>
        )}
      </Card>
    </div>
  );
};

export default BasicTablePage;
