import React, { useState, useMemo } from "react";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import { FaStar } from "react-icons/fa";
import { X } from "lucide-react";
import { DEFAULT_USER_ICON } from "@/config";
import { toast } from "react-toastify";

const Sellers = ({ requestId, assignedSellers, onClose }) => {
  // API hooks
  const {
    data: sellerData,
    isLoading,
    isError
  } = useGetApiQuery(`/admin/users?role=Seller`);

  const [postApi, { isLoading: isSubmitting }] = usePostApiMutation();

  // State
  const [notes, setNotes] = useState("");
  const [selectedSellers, setSelectedSellers] = useState([]);

  // Filter out already assigned sellers from the available sellers list
  const availableSellers = useMemo(() => {
    if (!sellerData?.data) return [];
    
    const assignedSellerIds = assignedSellers?.map(assignment => assignment.seller.id) || [];
    return sellerData.data.filter(seller => !assignedSellerIds.includes(seller.id));
  }, [sellerData?.data, assignedSellers]);

  // Replace sellers with availableSellers in your component
  const sellers = availableSellers;

  const handleToggleSelect = (seller) => {
    if (selectedSellers.some((s) => s.id === seller.id)) {
      setSelectedSellers(selectedSellers.filter((s) => s.id !== seller.id));
    } else {
      setSelectedSellers([...selectedSellers, seller]);
    }
  };

  const isSelected = (id) => selectedSellers.some((s) => s.id === id);

  // Handle form submission
  const handleSubmit = async () => {
    if (selectedSellers.length === 0) {
      toast.error("Please select at least one seller");
      return;
    }

    if (!requestId) {
      toast.error("Request ID is missing");
      return;
    }

    try {
      // Prepare payload
      const payload = {
        request_id: requestId,
        seller_ids: selectedSellers.map(seller => seller.id),
        notes: notes.trim() || ''
      };

      // Make API call
      const response = await postApi({
        end_point: 'admin/requests/assign-sellers',
        body: payload
      });

      // Handle response
      if (response.error) {
        toast.error(response.error.data?.message || "Failed to assign sellers");
        console.error("Error assigning sellers:", response.error);
      } 
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error("Error assigning sellers:", error);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="p-4 bg-white shadow-md rounded-lg mt-6 flex justify-center items-center h-64">
        <div className="text-center">
          <svg className="animate-spin h-10 w-10 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-4 text-gray-600">Loading sellers...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className="p-4 bg-white shadow-md rounded-lg mt-6">
        <div className="bg-red-50 p-4 rounded-md">
          <h3 className="text-red-800 font-medium">Error Loading Sellers</h3>
          <p className="text-red-600 mt-2">Unable to load sellers. Please try again later.</p>
          <button
            className="mt-4 bg-red-100 text-red-800 px-4 py-2 rounded hover:bg-red-200"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white shadow-md rounded-lg mt-6">
      <h2 className="text-lg font-semibold mb-4">Select Seller</h2>

      {/* Filters */}
      <div className="flex gap-2 mb-4">
        <select className="border p-2 rounded w-48 text-sm">
          <option>Category Name</option>
        </select>
        <select className="border p-2 rounded w-48 text-sm">
          <option>Sub Category Name</option>
        </select>
        <input
          type="text"
          placeholder="Search Seller"
          className="border p-2 rounded flex-1 text-sm"
        />
        <label className="flex items-center gap-1 text-sm">
          <input type="checkbox" />
          Select all
        </label>
      </div>

      {/* Selected Sellers */}
      {selectedSellers.length > 0 && (
        <div className="border p-2 rounded mb-4 flex flex-wrap gap-2 bd-gray-100">
          {selectedSellers.map((seller) => (
            <div
              key={seller.id}
              className="flex items-center gap-2 border rounded p-2 w-64 border-blue-500 bg-blue-50 cursor-pointer"
              onClick={() => handleToggleSelect(seller)}
            >
              <input
                type="checkbox"
                checked
                onChange={(e) => {
                  e.stopPropagation();
                  handleToggleSelect(seller);
                }}
                className="cursor-pointer"
              />
              <img
                src={seller.profile_picture_url
                  ? import.meta.env.VITE_ASSET_HOST_URL + seller.profile_picture_url
                  : DEFAULT_USER_ICON
                }
                alt={seller.first_name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <p className="text-sm font-medium">
                  {seller.first_name} {seller.last_name}
                </p>
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <FaStar key={i} />
                  ))}
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleSelect(seller);
                }}
                className="text-gray-400 hover:text-red-500"
              >
                <X size={16} />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Seller List */}
      <div className="grid grid-cols-3 gap-4 max-h-[400px] overflow-y-auto">
        {sellers.map((seller) => (
          <div
            key={seller.id}
            className={`flex items-center gap-2 border rounded p-2 cursor-pointer transition-all ${
              isSelected(seller.id)
                ? 'border-blue-500 bg-blue-50'
                : 'hover:border-gray-400 hover:bg-gray-50'
            }`}
            onClick={() => handleToggleSelect(seller)}
          >
            <input
              type="checkbox"
              checked={isSelected(seller.id)}
              onChange={(e) => {
                // Prevent the div's onClick from firing when clicking directly on the checkbox
                e.stopPropagation();
                handleToggleSelect(seller);
              }}
              className="cursor-pointer"
            />
            <img
              src={seller.profile_picture_url
                ? import.meta.env.VITE_ASSET_HOST_URL + seller.profile_picture_url
                : DEFAULT_USER_ICON
              }
              alt={seller.first_name}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <p className="text-sm font-medium">
                {seller.first_name} {seller.last_name}
              </p>
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <FaStar key={i} />
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Notes */}
      <div className="mt-6">
        <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
          Notes (Optional)
        </label>
        <textarea
          id="notes"
          rows={3}
          className="w-full border rounded-md p-2 text-sm"
          placeholder="Add any notes about this assignment..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </div>

      {/* Footer */}
      <div className="flex justify-end gap-2 mt-6">
        <button
          className="border px-4 py-2 rounded text-sm"
          onClick={onClose}
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          className={`bg-indigo-600 text-white px-4 py-2 rounded text-sm flex items-center ${
            isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:bg-indigo-700'
          }`}
          onClick={handleSubmit}
          disabled={isSubmitting || selectedSellers.length === 0}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </>
          ) : (
            'Assign Sellers'
          )}
        </button>
      </div>
    </div>
  );
};

export default Sellers;
