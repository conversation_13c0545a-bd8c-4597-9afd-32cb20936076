import React, { useCallback, useState } from "react";
import { FiUpload, FiX, FiImage, FiAlertCircle } from "react-icons/fi";

const Fileinput = ({
  title,
  name,
  label = "Browse",
  onChange,
  placeholder = "Drag & drop files here or click to browse",
  multiple = false,
  preview = true,
  className = "",
  id = name,
  selectedFile,
  accept = "image/*",
  badge,
  selectedFiles = [],
  setFieldValue,
  fieldName,
  previewImage,
  setPreviewImage,
  required = false,
  error,
  onBlur,
}) => {
  const inputRef = React.useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [localPreview, setLocalPreview] = useState(null);

  // Handle file processing
  const processFile = useCallback((file) => {
    if (!file) return;

    if (setFieldValue && fieldName) {
      setFieldValue(fieldName, file);
      setFieldValue(`${fieldName}Touched`, true);
      setFieldValue('errors.' + fieldName, undefined);

      if (required) {
        setFieldValue(`${fieldName}Error`, null);
      }
    }

    if (preview && file.type.startsWith('image/')) {
      const previewUrl = URL.createObjectURL(file);
      setLocalPreview(previewUrl);
      if (setPreviewImage) {
        setPreviewImage(previewUrl);
      }
    }

    if (onChange) {
      onChange({ target: { files: [file] } });
    }
  }, [setFieldValue, fieldName, preview, setPreviewImage, onChange, required]);

  const handleFileChange = (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  const handleLabelClick = () => {
    inputRef.current?.click();
  };

  const removeFile = () => {
    if (inputRef.current) {
      inputRef.current.value = '';
    }
    setLocalPreview(null);
    if (setPreviewImage) {
      setPreviewImage(null);
    }
    if (setFieldValue && fieldName) {
      setFieldValue(fieldName, null);
    }
  };

  // Determine what to show in the dropzone
  const renderDropzoneContent = () => {
    if (localPreview || previewImage) {
      return (
        <div className="relative group w-full h-full flex items-center justify-center">
          <img
            src={localPreview || previewImage}
            alt="Preview"
            className="h-[120px] w-auto max-w-full object-contain rounded-md"
          />
          <button
            type="button"
            onClick={removeFile}
            className="absolute top-2 right-2 bg-white/80 hover:bg-white rounded-full p-1.5 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Remove image"
          >
            <FiX className="w-4 h-4 text-red-500" />
          </button>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center p-4 text-center">
        <FiUpload className="w-8 h-8 text-gray-400 mb-2" />
        <p className="text-sm text-gray-500">{placeholder}</p>
        <p className="text-xs text-gray-400 mt-1">Supports: JPG, PNG, GIF</p>
      </div>
    );
  };

  return (
    <div className={`mb-4 ${className}`}>
      {title && (
        <label className="block mb-2 text-sm font-medium text-gray-700">
          {title}
          {required && <span className="text-red-500"> *</span>}
        </label>
      )}

      <div
        className={`relative border-2 border-dashed rounded-lg p-4 transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : error
              ? 'border-red-300 bg-red-50'
              : 'border-gray-300 hover:border-gray-400 bg-gray-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleLabelClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === 'Enter' && handleLabelClick()}
      >
        {/* Hidden file input */}
        <input
          ref={inputRef}
          type="file"
          onChange={handleFileChange}
          className="absolute opacity-0 w-0 h-0"
          name={name}
          id={id}
          accept={accept}
          multiple={multiple}
          required={required}
          onBlur={onBlur}
        />

        {/* Dropzone content */}
        <div className="min-h-[150px] flex items-center justify-center">
          {renderDropzoneContent()}
        </div>

        {/* Browse button */}
        <div className="mt-2 flex justify-center">
          <button
            type="button"
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            onClick={(e) => {
              e.stopPropagation();
              handleLabelClick();
            }}
          >
            {label}
          </button>
        </div>
      </div>

      {error && (
        <div className="mt-1 text-sm text-red-600 flex items-center">
          <FiAlertCircle className="mr-1 w-4 h-4" />
          {error}
        </div>
      )}
    </div>
  );
};

export default Fileinput;