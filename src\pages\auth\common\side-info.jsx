import React from "react";
import { motion } from "framer-motion";
import useDarkMode from "@/hooks/useDarkMode";
import { Link } from "react-router-dom";
import LogoWhite from "@/assets/images/logo/logo-white.svg";
import Logo from "@/assets/images/auth/logo.png";
import Illustration from "@/assets/images/auth/authImage.jpg";

const SideInfo = () => {
  const [isDark] = useDarkMode();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="flex flex-col justify-center bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 px-10 py-16 max-w-lg w-full"
    >
      <motion.div
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        <Link to="/" className="block mb-8">
          <img src="/site-logo.svg" alt="" className="h-10 w-auto" />
        </Link>
        <h1 className="text-4xl font-extrabold text-gray-800 dark:text-gray-100 mb-4">
          Unlock Your Project
        </h1>
      </motion.div>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4, delay: 0.4 }}
      >
        <img
          src={Illustration}
          alt=""
          className="w-full object-contain opacity-90 mix-blend-multiply"
        />
      </motion.div>
    </motion.div>
  );
};

export default SideInfo;
