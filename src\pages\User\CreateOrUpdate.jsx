"use client";
import React, { useState, useEffect, useRef } from 'react';
import { Formik, Form, Field } from 'formik';
import { usePostApiMutation, useUpdateApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import { useNavigate, useParams } from 'react-router-dom';
import { skipToken } from "@reduxjs/toolkit/query";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Card from "@/components/ui/Card";
import { toast } from 'react-toastify';
import { DEFAULT_USER_ICON } from "@/config";
import { getInitialValues, createUserValidationSchema, updateUserValidationSchema } from "./FormSettings"
const CreateUserPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const formikRef = useRef();
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Get user data for edit mode
  const { data: userData } = useGetApiQuery(
    id ? `admin/users/${id}` : skipToken
  );

  // API mutation hooks
  const [postApi] = usePostApiMutation();
  const [updateApi] = useUpdateApiMutation();

  // Load user data when in edit mode
  useEffect(() => {
    if (id && userData?.data && formikRef.current) {
      const user = userData.data;

      // Set form values from user data
      formikRef.current.setValues({
        ...getInitialValues(),
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        phone_number: user.phone_number || '',
        father_name: user.father_name || '',
        mother_name: user.mother_name || '',
        date_of_birth: user.date_of_birth ? new Date(user.date_of_birth).toISOString().split('T')[0] : '',
        gender: user.gender || '',
        address: user.address || '',
        national_id_number: user.national_id_number || '',
        passport_number: user.passport_number || '',
        role: user.role || 'Buyer',
      });
    }
  }, [id, userData, formikRef]);

  // Form submission handler
  const handleSubmit = async (values, formikHelpers = {}) => {
    const { setSubmitting = () => {}, resetForm = () => {}, setErrors = () => {} } = formikHelpers;

    try {
      setSubmitting(true);
      const formData = new FormData();

      // Required fields
      formData.append('first_name', values.first_name);
      formData.append('last_name', values.last_name);
      formData.append('email', values.email);
      formData.append('phone_number', values.phone_number);
      formData.append('role', values.role);

      // Only add password if provided (required for new users, optional for updates)
      if (values.password) {
        formData.append('password', values.password);
        formData.append('password_confirmation', values.password_confirmation);
      }

      // Optional fields
      if (values.father_name) formData.append('father_name', values.father_name);
      if (values.mother_name) formData.append('mother_name', values.mother_name);
      if (values.date_of_birth) formData.append('date_of_birth', values.date_of_birth);
      if (values.gender) formData.append('gender', values.gender);
      if (values.address) formData.append('address', values.address);
      if (values.national_id_number) formData.append('national_id_number', values.national_id_number);
      if (values.passport_number) formData.append('passport_number', values.passport_number);

      // Avatar handling - only in create mode
      if (!id && values.avatar instanceof File) {
        formData.append('avatar', values.avatar);
      }

      // Make API request (POST for create, PUT for update)
      const response = id
        ? await updateApi({
            end_point: `admin/users/${id}`,
            body: formData,
            _method: 'PUT' // For update requests
          })
        : await postApi({
            end_point: 'admin/users',
            body: formData
          });

      if (response.error) {
        console.log('API Response:', response);

        // Handle validation errors from the API
        if (response.error.status === 422 && response.error.data) {
          // Extract error messages from the API response
          const apiErrors = {};

          // Check if the error is in the expected format
          if (response.error.data.error) {
            // Map API errors to form fields
            Object.entries(response.error.data.error).forEach(([field, messages]) => {
              apiErrors[field] = Array.isArray(messages) ? messages[0] : messages;
            });

            // Set the errors in the form
            setErrors(apiErrors);

            // Show error toast
            toast.error(response.error.data.message || 'Validation failed. Please check the form.');
          } else {
            // Generic error handling
            toast.error(response.error.data.message || 'An error occurred while saving the user.');
          }
        } else {
          // Handle other types of errors
          toast.error(response.error.data?.message || 'An error occurred while saving the user.');
        }
      } else {
        resetForm();
        // Navigate back to the appropriate user list based on role
        const role = values.role.toLowerCase();
        navigate(`/users/${role}`);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="mx-auto px-4">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-bold text-gray-800">
          {id ? 'Edit User' : 'Create New User'}
        </h1>
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back
        </Button>
      </div>

      <Formik
        innerRef={formikRef}
        initialValues={getInitialValues()}
        enableReinitialize
        validationSchema={id ? updateUserValidationSchema : createUserValidationSchema }
        onSubmit={handleSubmit}
        validateOnBlur
        validateOnChange
      >
        {({ isSubmitting, values, setFieldValue, handleSubmit, handleBlur, errors, validateForm, setTouched }) => (
          <Form className="space-y-6">
            {/* Error Summary */}
            {submitAttempted && Object.keys(errors).length > 0 && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      There {Object.keys(errors).length === 1 ? 'was' : 'were'} {Object.keys(errors).length} error{Object.keys(errors).length === 1 ? '' : 's'} with your submission
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <ul className="list-disc pl-5 space-y-1">
                        {Object.entries(errors).map(([field, error]) => (
                          typeof error === 'string' && (
                            <li key={field}>{error}</li>
                          )
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left Column - Basic Info */}
              <div className="space-y-6">
                <Card title="Basic Information" className="p-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <InputField
                        name="first_name"
                        label="First Name"
                        type="text"
                        required
                        placeholder="John"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.first_name}
                      />
                      <InputField
                        name="last_name"
                        label="Last Name"
                        type="text"
                        required
                        placeholder="Doe"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.last_name}
                      />
                    </div>

                    <InputField
                      name="email"
                      label="Email Address"
                      type="email"
                      required
                      placeholder="<EMAIL>"
                      onBlur={handleBlur}
                      error={submitAttempted && errors.email}
                    />

                    <InputField
                      name="phone_number"
                      label="Phone Number"
                      type="text"
                      required
                      placeholder="+1234567890"
                      onBlur={handleBlur}
                      error={submitAttempted && errors.phone_number}
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Gender
                      </label>
                      <div className="grid grid-cols-3 gap-3">
                        {['male', 'female', 'other'].map((gender) => (
                          <label key={gender} className="flex items-center">
                            <Field
                              type="radio"
                              name="gender"
                              value={gender}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                            />
                            <span className="ml-2 text-sm text-gray-700 capitalize">
                              {gender}
                            </span>
                          </label>
                        ))}
                      </div>
                      {submitAttempted && errors.gender && (
                        <p className="mt-1 text-sm text-red-600">{errors.gender}</p>
                      )}
                    </div>

                    <InputField
                      name="date_of_birth"
                      label="Date of Birth"
                      type="date"
                      placeholder="YYYY-MM-DD"
                      onBlur={handleBlur}
                      error={submitAttempted && errors.date_of_birth}
                    />
                  </div>
                </Card>

                {!id && (
                  <Card title="Account Security" className="p-6">
                    <div className="space-y-4">
                      <div className="relative">
                        <InputField
                          name="password"
                          label="Password"
                          type={showPassword ? "text" : "password"}
                          required
                          placeholder="••••••••"
                          onBlur={handleBlur}
                          error={submitAttempted && errors.password}
                        />
                        <button
                          type="button"
                          className="absolute right-2 top-9 text-gray-500 hover:text-gray-700"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          )}
                        </button>
                      </div>
                      <div className="relative">
                        <InputField
                          name="password_confirmation"
                          label="Confirm Password"
                          type={showConfirmPassword ? "text" : "password"}
                          required
                          placeholder="••••••••"
                          onBlur={handleBlur}
                          error={submitAttempted && errors.password_confirmation}
                        />
                        <button
                          type="button"
                          className="absolute right-2 top-9 text-gray-500 hover:text-gray-700"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </Card>
                )}
              </div>

              {/* Middle Column - Additional Info */}
              <div className="space-y-6">
                <Card title="Additional Information" className="p-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <InputField
                        name="father_name"
                        label="Father's Name"
                        type="text"
                        placeholder="John Doe Sr."
                        onBlur={handleBlur}
                        error={submitAttempted && errors.father_name}
                      />
                      <InputField
                        name="mother_name"
                        label="Mother's Name"
                        type="text"
                        placeholder="Jane Doe"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.mother_name}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <InputField
                        name="national_id_number"
                        label="National ID"
                        type="text"
                        placeholder="1234567890"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.national_id_number}
                      />
                      <InputField
                        name="passport_number"
                        label="Passport Number"
                        type="text"
                        placeholder="*********"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.passport_number}
                      />
                    </div>


                  </div>
                </Card>

                <Card title="Address Information" className="p-6">
                  <div className="space-y-4">
                    <Textarea
                      name="address"
                      label="Street Address"
                      rows={2}
                      placeholder="123 Main St, Apt 4B"
                      value={values.address}
                      onChange={(e) => setFieldValue('address', e.target.value)}
                      onBlur={handleBlur}
                      error={submitAttempted && errors.address}
                    />

                    {/* City, State, ZIP, and Country fields removed as requested */}
                  </div>
                </Card>
              </div>

              {/* Right Column - Account Settings */}
              {!id ? (
              <div className="space-y-6">
                <Card title="Account Settings" className="p-6">
                  <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          User Role
                        </label>
                        <Field
                          as="select"
                          name="role"
                          className={`mt-1 block w-full pl-3 pr-10 py-2 text-base border ${
                            submitAttempted && errors.role ? 'border-red-500' : 'border-gray-300'
                          } focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md`}
                        >
                          <option value="Buyer">Buyer</option>
                          <option value="Seller">Seller</option>
                          <option value="Supplier">Supplier</option>
                          <option value="Admin">Admin</option>
                        </Field>
                        {submitAttempted && errors.role && (
                          <p className="mt-1 text-sm text-red-600">{errors.role}</p>
                        )}
                      </div>

                  </div>
                </Card>
                  <Card title="Profile Picture" className="p-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Avatar
                        </label>
                        <input
                          type="file"
                          name="avatar"
                          accept="image/*"
                          className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                            submitAttempted && errors.avatar ? 'border-red-500' : 'border-gray-300'
                          }`}
                          onChange={(event) => {
                            const file = event.currentTarget.files[0];
                            setFieldValue("avatar", file);
                          }}
                        />
                        {submitAttempted && errors.avatar && (
                          <p className="mt-1 text-sm text-red-600">{errors.avatar}</p>
                        )}
                        {values.avatar && values.avatar instanceof File && (
                          <div className="mt-2">
                            <img
                              src={URL.createObjectURL(values.avatar)}
                              alt="Avatar Preview"
                              className="h-24 w-24 rounded-full object-cover border-2 border-gray-200"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
              </div>
            ):
            <Card className="p-4 hover:shadow-md transition-shadow duration-300">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="h-16 w-16 rounded-full overflow-hidden border-2 border-primary-100 shadow-sm">
                    <img
                      src={
                        userData?.data?.profile_picture_url
                          ? `${import.meta.env.VITE_ASSET_HOST_URL}${userData?.data?.profile_picture_url}`
                          : DEFAULT_USER_ICON
                      }
                      alt="User Avatar"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute -bottom-1 -right-1 bg-white p-0.5 rounded-full border border-gray-200">
                    <span className={`inline-block w-4 h-4 rounded-full ${userData?.data?.status === 'active' ? 'bg-success-500' : 'bg-warning-500'}`}></span>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {userData?.data?.first_name} {userData?.data?.last_name}
                    </h3>
                    {userData?.data?.roles?.map((role) => (
                      <span
                        key={role}
                        className="px-2 py-1 text-xs font-medium rounded-full bg-primary-50 text-primary-700 border border-primary-100"
                      >
                        {role}
                      </span>
                    ))}
                  </div>
                  <div className="mt-1 flex items-center text-sm text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    {userData?.data?.email}
                  </div>
                  <div className="mt-1 flex items-center text-sm text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    {userData?.data?.phone_number}
                  </div>
                </div>
              </div>
            </Card>

            }
            </div>

            <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(-1)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="primary"
                isLoading={isSubmitting}
                onClick={async () => {
                  setSubmitAttempted(true);
                  const touchedFields = {};
                  Object.keys(values).forEach(key => {
                    touchedFields[key] = true;
                  });
                  setTouched(touchedFields);

                  const errors = await validateForm();
                  if (Object.keys(errors).length === 0) {
                    handleSubmit();
                  }
                }}
              >
                {id ? 'Update User' : 'Create User'}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateUserPage;