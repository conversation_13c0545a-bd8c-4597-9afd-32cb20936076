import React, { useState, useMemo, useEffect } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Link } from "react-router-dom";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Loading from "@/components/Loading";
import Badge from "@/components/ui/Badge";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";
import DeleteModal from "./DeleteModal";
import BulkApproveModal from "./BulkApproveModal";
import BulkRejectModal from "./BulkRejectModal";
import BulkDeleteModal from "./BulkDeleteModal";
import EditRequestModal from "./EditRequestModal";
import MergeRequestsModal from "./MergeRequestsModal";
import { toast } from "react-toastify";
import Button from "@/components/ui/Button";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";

const FilterCategory = ({ onFilterChange }) => {
  const { data: categories, isLoading: isCategoriesLoading } = useGetApiQuery(
    "admin/categories"
  );

  const [category, setCategory] = useState('');
  const [subCategory, setSubCategory] = useState('');
  const [selectedCategoryData, setSelectedCategoryData] = useState(null);

  // Get subcategories for the selected category
  const subCategories = useMemo(() => {
    if (!category || !categories?.data) return [];
    const selectedCategory = categories.data.find(cat => cat.id === category);
    setSelectedCategoryData(selectedCategory);
    return selectedCategory?.sub_categories || [];
  }, [category, categories]);

  // Apply filters function
  const applyFilters = (categoryId = category, subCategoryId = subCategory) => {
    let filterParams = '';

    if (categoryId) {
      filterParams += `&category_id=${categoryId}`;
    }

    if (subCategoryId) {
      filterParams += `&sub_category_id=${subCategoryId}`;
    }

    onFilterChange(filterParams);
  };

  // Handle category change
  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    setCategory(categoryId);
    setSubCategory(''); // Reset subcategory when category changes
    // Automatically apply filters when category changes
    applyFilters(categoryId, '');
  };

  // Handle subcategory change
  const handleSubCategoryChange = (e) => {
    const subCategoryId = e.target.value;
    setSubCategory(subCategoryId);
    // Automatically apply filters when subcategory changes
    applyFilters(category, subCategoryId);
  };

  // Clear filters
  const clearFilters = () => {
    setCategory('');
    setSubCategory('');
    onFilterChange('');
  };

  return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          {/* <label className="block text-sm font-medium text-gray-700 mb-1">Category</label> */}
          <select
            className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            value={category}
            onChange={handleCategoryChange}
            disabled={isCategoriesLoading}
          >
            <option value="">All Categories</option>
            {categories?.data?.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.title}
              </option>
            ))}
          </select>
        </div>

        <div>
          {/* <label className="block text-sm font-medium text-gray-700 mb-1">Sub Category</label> */}
          <select
            className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            value={subCategory}
            onChange={handleSubCategoryChange}
            disabled={!category || subCategories.length === 0}
          >
            <option value="">All Sub Categories</option>
            {subCategories.map((subCat) => (
              <option key={subCat.id} value={subCat.id}>
                {subCat.title}
              </option>
            ))}
          </select>
        </div>

        {/* <div className="flex items-end">
          <Button
            onClick={clearFilters}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800"
          >
            Clear
          </Button>
        </div> */}
      </div>
  );
}

const BulkActionsDropdown = ({ selectedCount, onBulkAction }) => {
  if (selectedCount === 0) return null;

  return (
    <div className="flex items-center gap-2">
 
      <Dropdown
        classMenuItems="right-0 w-[140px] top-[110%]"
        label={
          <div className="flex items-center gap-2 px-3 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600">
            <Icon icon="heroicons:chevron-down" className="w-4 h-4" />
            Bulk Actions ({selectedCount})
          </div>
        }
      >
        <div className="divide-y divide-slate-100 dark:divide-slate-800">
          <Menu.Item>
            <div
              className="hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:bg-opacity-50 cursor-pointer flex items-center px-3 py-2 text-sm text-slate-600 dark:text-slate-300"
              onClick={() => onBulkAction('approve')}
            >
              <Icon icon="heroicons:check" className="w-4 h-4 mr-2 text-success-500" />
              Approve
            </div>
          </Menu.Item>
          <Menu.Item>
            <div
              className="hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:bg-opacity-50 cursor-pointer flex items-center px-3 py-2 text-sm text-slate-600 dark:text-slate-300"
              onClick={() => onBulkAction('reject')}
            >
              <Icon icon="heroicons:x-mark" className="w-4 h-4 mr-2 text-warning-500" />
              Reject
            </div>
          </Menu.Item>
          <Menu.Item>
            <div
              className={`hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:bg-opacity-50 cursor-pointer flex items-center px-3 py-2 text-sm text-slate-600 dark:text-slate-300 ${selectedCount < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => selectedCount >= 2 && onBulkAction('merge')}
            >
              <Icon icon="heroicons:arrows-pointing-in" className="w-4 h-4 mr-2 text-info-500" />
              Merge ({selectedCount >= 2 ? 'Available' : 'Need 2+'})
            </div>
          </Menu.Item>
          <Menu.Item>
            <div
              className="hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:bg-opacity-50 cursor-pointer flex items-center px-3 py-2 text-sm text-slate-600 dark:text-slate-300"
              onClick={() => onBulkAction('delete')}
            >
              <Icon icon="heroicons:trash" className="w-4 h-4 mr-2 text-danger-500" />
              Delete
            </div>
          </Menu.Item>
        </div>
      </Dropdown>
    </div>
  );
};

const RequestPage = () => {
  const { statusParam } = useParams();
  const location = useLocation();

  // Extract search query from URL if it exists
  const searchParams = new URLSearchParams(location.search);
  const searchQuery = searchParams.get('search') || '';

  // Initialize apiParam with search query if it exists
  const [apiParam, setApiParam] = useState(searchQuery ? `&search=${searchQuery}` : '');
  const [isFiltering, setIsFiltering] = useState(false);

  const queryParam = useMemo(() => {
    const base = statusParam ? `?status=${statusParam.charAt(0).toUpperCase() + statusParam.slice(1)}` : '';
    return apiParam ? `${base}${apiParam}` : base;
  }, [statusParam, apiParam]);

  const navigate = useNavigate();

  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMergeModal, setShowMergeModal] = useState(false);
  const [approveData, setApproveData] = useState(null);
  const [rejectData, setRejectData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const [editData, setEditData] = useState(null);

  // Bulk action states
  const [selectedRequests, setSelectedRequests] = useState([]);
  const [showBulkApproveModal, setShowBulkApproveModal] = useState(false);
  const [showBulkRejectModal, setShowBulkRejectModal] = useState(false);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);

  const {
    data: requests,
    isLoading,
    isError,
    isFetching,
    refetch
  } = useGetApiQuery(`/admin/requests${queryParam}`);

  // Reset filtering state when data is loaded
  useEffect(() => {
    if (!isFetching && requests) {
      setIsFiltering(false);
    }
  }, [isFetching, requests]);

  // Handle category filter changes
  const handleFilterChange = (filterParams) => {
    // Set filtering state to prevent loading screen
    setIsFiltering(true);

    // Preserve search parameter when applying filters
    const searchParams = new URLSearchParams(location.search);
    const searchQuery = searchParams.get('search') || '';

    let newParams = '';
    if (searchQuery) {
      newParams += `&search=${searchQuery}`;
    }

    // Add the category filters
    newParams += filterParams;

    setApiParam(newParams);
  };

  const handleRequestClick = (id) => {
    navigate(`/request/${id}`);
  };

  // Bulk selection functions
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      const allRequestIds = requests?.data?.map(request => request.id) || [];
      setSelectedRequests(allRequestIds);
    } else {
      setSelectedRequests([]);
    }
  };

  const handleSelectRequest = (requestId) => {
    setSelectedRequests(prev => {
      if (prev.includes(requestId)) {
        return prev.filter(id => id !== requestId);
      } else {
        return [...prev, requestId];
      }
    });
  };

  const isAllSelected = requests?.data?.length > 0 && selectedRequests.length === requests?.data?.length;
  const isIndeterminate = selectedRequests.length > 0 && selectedRequests.length < (requests?.data?.length || 0);

  // Bulk action handlers
  const handleBulkAction = (action) => {
    if (selectedRequests.length === 0) {
      toast.error("Please select at least one request");
      return;
    }

    if (action === 'merge' && selectedRequests.length < 2) {
      toast.error("Please select at least 2 requests to merge");
      return;
    }

    switch (action) {
      case 'approve':
        setShowBulkApproveModal(true);
        break;
      case 'reject':
        setShowBulkRejectModal(true);
        break;
      case 'merge':
        setShowMergeModal(true);
        break;
      case 'delete':
        setShowBulkDeleteModal(true);
        break;
      default:
        break;
    }
  };

  const handleBulkSuccess = () => {
    setSelectedRequests([]);
    refetch();
  };

  const tableData = requests?.data?.map((item) => {
    return {
      id: item.id,
      select: (
        <input
          type="checkbox"
          checked={selectedRequests.includes(item.id)}
          onChange={() => handleSelectRequest(item.id)}
          className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
        />
      ),
      request_code: (
        <span className="font-mono text-sm text-gray-600">
          {item.request_code || `REQ-${item.id}`}
        </span>
      ),
      title: (
        <button
          type="button"
          onClick={() => handleRequestClick(item.id)}
          className="hover:text-primary-500 hover:underline"
        >
          {item.title}
        </button>
      ),
      buyer: item.buyer.name,
      category: item.category.title,
      subCategory: item.sub_category.title,
      budget: `$${item.budget_min} - $${item.budget_max}`,
      deadline: new Date(item.deadline).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
      urgency: (
        <Badge
          className={
            item.urgency === "High"
              ? "bg-red-500 text-white"
              : item.urgency === "Medium"
              ? "bg-blue-500 text-white"
              : "bg-green-500 text-white"
          }
        >
          {item.urgency}
        </Badge>
      ),
      status: (
        <Badge
          className={
            item.status === "Pending"
              ? "bg-yellow-700 text-white"
              : item.status === "Approved"
              ? "bg-green-500 text-white"
              : "bg-red-500 text-white"
          }
        >
          {item.status}
        </Badge>
      )
    };
  });

  const changePage = (val) => {
    // Preserve search parameter when changing pages
    const searchParams = new URLSearchParams(location.search);
    const searchQuery = searchParams.get('search') || '';

    if (val) {
      // If we have a page number, add it to the query
      setApiParam(searchQuery ? `&search=${searchQuery}&page=${val}` : `&page=${val}`);
    } else {
      // If no page number (e.g., page 1), just keep the search
      setApiParam(searchQuery ? `&search=${searchQuery}` : '');
    }
  };

  const columns = [
    {
      label: (
        <input
          type="checkbox"
          checked={isAllSelected}
          ref={(input) => {
            if (input) input.indeterminate = isIndeterminate;
          }}
          onChange={handleSelectAll}
          className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
        />
      ),
      field: "select",
    },
    {
      label: "Request Code",
      field: "request_code",
    },
    {
      label: "Title",
      field: "title",
    },
    {
      label: "Buyer",
      field: "buyer",
    },
    {
      label: "Category",
      field: "category",
    },
    {
      label: "Sub Category",
      field: "subCategory",
    },
    {
      label: "Budget",
      field: "budget",
    },
    {
      label: "Deadline",
      field: "deadline",
    },
    {
      label: "Urgency",
      field: "urgency",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const actions = [
      {
        name: "Edit",
        icon: "heroicons:pencil-square",
        onClick: (val) => {
          setEditData(requests.data[val]);
          setShowEditModal(true);
        },
        className: "text-primary-500 hover:text-primary-600"
      },
      {
        name: "Approve",
        icon: "heroicons:check-square",
        onClick: (val) => {
          setApproveData(requests.data[val]);
          setShowApproveModal(true);
        },
        hidden: requests?.data[0]?.status === 'Approved',
      },
      {
        name: "Reject",
        icon: "heroicons:x-circle",
        onClick: (val) => {
          setRejectData(requests.data[val]);
          setShowRejectModal(true);
        },

        className: "text-warning-500 hover:text-warning-600"
      },
      {
        name: "Delete",
        icon: "heroicons:trash",
        onClick: (val) => {
          setDeleteData(requests.data[val]);
          setShowDeleteModal(true);
        },
        className: "text-danger-500 hover:text-danger-600"
      },
    ];


  return (
    <div className="flex flex-col gap-5">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">
          {statusParam ? `Requests ${statusParam.charAt(0).toUpperCase() + statusParam.slice(1)}` : 'Requests'}
        </h2>
        <Link to="/requests" className="btn btn-primary">
          Back
        </Link>
      </div>

      {(isLoading || (isFetching && !isFiltering)) ? (
        <Loading />
      ) : isError ? (
        <p className="text-red-500">Something went wrong</p>
      ) : (
        <BasicTablePage
          tableHeaderExtra={
            <div className="flex items-center gap-4">
              <FilterCategory onFilterChange={handleFilterChange} />
              <BulkActionsDropdown
                selectedCount={selectedRequests.length}
                onBulkAction={handleBulkAction}
              />
            </div>
          }
          title={`${statusParam ? statusParam.charAt(0).toUpperCase() + statusParam.slice(1) : ''} Requests`}
          columns={columns}
          actions={actions}
          data={tableData}
          changePage={changePage}
          currentPage={requests?.current_page}
          setFilter={setApiParam}
          totalPages={Math.ceil(requests?.total / requests?.per_page)}
          hideCreateButton={true}
        />
      )}

      {showApproveModal && (
        <ApproveModal
          showApproveModal={showApproveModal}
          setShowApproveModal={(value) => {
            setShowApproveModal(value);
            if (!value) refetch();
          }}
          data={approveData}
        />
      )}

      {showRejectModal && (
        <RejectModal
          showRejectModal={showRejectModal}
          setShowRejectModal={(value) => {
            setShowRejectModal(value);
            if (!value) refetch();
          }}
          data={rejectData}
        />
      )}

      {showDeleteModal && (
        <DeleteModal
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
          onSuccess={refetch}
        />
      )}

      {/* Bulk Action Modals */}
      {showBulkApproveModal && (
        <BulkApproveModal
          showModal={showBulkApproveModal}
          setShowModal={setShowBulkApproveModal}
          selectedRequests={selectedRequests}
          onSuccess={handleBulkSuccess}
        />
      )}

      {showBulkRejectModal && (
        <BulkRejectModal
          showModal={showBulkRejectModal}
          setShowModal={setShowBulkRejectModal}
          selectedRequests={selectedRequests}
          onSuccess={handleBulkSuccess}
        />
      )}

      {showBulkDeleteModal && (
        <BulkDeleteModal
          showModal={showBulkDeleteModal}
          setShowModal={setShowBulkDeleteModal}
          selectedRequests={selectedRequests}
          onSuccess={handleBulkSuccess}
        />
      )}

      {/* Edit Request Modal */}
      {showEditModal && (
        <EditRequestModal
          showModal={showEditModal}
          setShowModal={(value) => {
            setShowEditModal(value);
            if (!value) refetch();
          }}
          requestData={editData}
        />
      )}

      {/* Merge Requests Modal */}
      {showMergeModal && (
        <MergeRequestsModal
          showModal={showMergeModal}
          setShowModal={setShowMergeModal}
          selectedRequests={selectedRequests}
          onSuccess={handleBulkSuccess}
        />
      )}
    </div>
  );
};

export default RequestPage;
