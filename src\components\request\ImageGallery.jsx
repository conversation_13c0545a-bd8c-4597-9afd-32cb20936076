import React from "react";
import { FaFileAlt } from "react-icons/fa";
import { getFileIcon } from "@/utils/fileIcons";

const ImageGallery = ({ 
    imageAttachments, 
    selectedImageIndex, 
    setSelectedImageIndex, 
    request, 
    handleDocumentClick 
}) => {
    const previewImage = imageAttachments.length > 0 && imageAttachments[selectedImageIndex]?.file_path
        ? `${import.meta.env.VITE_ASSET_HOST_URL}${imageAttachments[selectedImageIndex].file_path}`
        : "/placeholder.jpg";

    return (
        <div>
            {/* Main Preview Area */}
            {imageAttachments.length > 0 ? (
                <img
                    src={previewImage}
                    alt="Preview"
                    className="w-full h-80 object-cover rounded-lg border"
                />
            ) : (
                <div className="w-full h-80 flex items-center justify-center bg-gray-100 rounded-lg border">
                    <p className="text-gray-500">No image attachments available</p>
                </div>
            )}

            {/* Image Thumbnails Section */}
            {imageAttachments.length > 0 && (
                <div className="mt-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Images</h3>
                    <div className="flex gap-2 overflow-x-auto pb-2">
                        {imageAttachments.map((att, index) => (
                            <img
                                key={att.id}
                                src={`${import.meta.env.VITE_ASSET_HOST_URL}${att.file_path}`}
                                alt={`Thumbnail ${index + 1}`}
                                className={`w-20 h-20 object-cover rounded-md border cursor-pointer transition ${
                                    selectedImageIndex === index
                                        ? 'border-2 border-blue-500 scale-105'
                                        : 'hover:scale-105 hover:border-gray-400'
                                }`}
                                onClick={() => setSelectedImageIndex(index)}
                            />
                        ))}
                        {(() => {
                            const filePath = request.file;
                            const fileName = filePath?.split('/').pop();
                            const fileExtension = fileName?.split('.').pop().toUpperCase();

                            return (
                                <div
                                    className="flex flex-col items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition w-32"
                                    onClick={() => handleDocumentClick(filePath)}
                                    title={`Click to open ${fileName} (${fileExtension})`}
                                >
                                    {getFileIcon(filePath)}
                                </div>
                            );
                        })()}
                    </div>
                </div>
            )}
        </div>
    );
};

export default ImageGallery;