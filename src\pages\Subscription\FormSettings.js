import * as Yup from 'yup';

export const getInitialValues = (data = null) => {
  return {
    name: data?.name || '',
    description: data?.description || '',
    price: data?.price || '',
    duration_days: data?.duration_days || 30,
    max_requests: data?.max_requests || '',
    max_offers: data?.max_offers || '',
    max_orders: data?.max_orders || '',
    is_active: data?.is_active ?? true,
    is_featured: data?.is_featured ?? false,
    user_type: data?.user_type || 'BOTH',
    features: {
      support: data?.features?.support || 'email',
      analytics: data?.features?.analytics ?? false,
      api_access: data?.features?.api_access ?? false,
      custom_branding: data?.features?.custom_branding ?? false,
      dedicated_manager: data?.features?.dedicated_manager ?? false,
      priority: data?.features?.priority || 'low',
    }
  };
};

export const validationSchema = Yup.object({
  name: Yup.string()
    .required('Plan name is required')
    .min(3, 'Plan name must be at least 3 characters')
    .max(100, 'Plan name must not exceed 100 characters'),
  
  description: Yup.string()
    .max(500, 'Description must not exceed 500 characters'),
  
  price: Yup.number()
    .required('Price is required')
    .min(0, 'Price must be 0 or greater')
    .max(9999.99, 'Price must not exceed 9999.99'),
  
  duration_days: Yup.number()
    .required('Duration is required')
    .min(1, 'Duration must be at least 1 day')
    .max(365, 'Duration must not exceed 365 days'),
  
  max_requests: Yup.number()
    .nullable()
    .min(1, 'Max requests must be at least 1 if specified')
    .max(10000, 'Max requests must not exceed 10000'),
  
  max_offers: Yup.number()
    .nullable()
    .min(1, 'Max offers must be at least 1 if specified')
    .max(10000, 'Max offers must not exceed 10000'),
  
  max_orders: Yup.number()
    .nullable()
    .min(1, 'Max orders must be at least 1 if specified')
    .max(10000, 'Max orders must not exceed 10000'),
  
  is_active: Yup.boolean(),
  is_featured: Yup.boolean(),
  
  user_type: Yup.string()
    .required('User type is required')
    .oneOf(['BUYER', 'SELLER', 'BOTH'], 'Invalid user type'),
  
  features: Yup.object({
    support: Yup.string()
      .oneOf(['email', 'phone', 'priority'], 'Invalid support type'),
    analytics: Yup.boolean(),
    api_access: Yup.boolean(),
    custom_branding: Yup.boolean(),
    dedicated_manager: Yup.boolean(),
    priority: Yup.string()
      .oneOf(['low', 'medium', 'high'], 'Invalid priority level'),
  })
});

export const USER_TYPE_OPTIONS = [
  { value: 'BUYER', label: 'Buyer' },
  { value: 'SELLER', label: 'Seller' },
  { value: 'BOTH', label: 'Both' }
];

export const SUPPORT_TYPE_OPTIONS = [
  { value: 'email', label: 'Email Support' },
  { value: 'phone', label: 'Phone Support' },
  { value: 'priority', label: 'Priority Support' }
];

export const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low Priority' },
  { value: 'medium', label: 'Medium Priority' },
  { value: 'high', label: 'High Priority' }
];
