export const menuItems = [
  {
    title: "Dashboard",
    isHide: true,
    icon: "heroicons-outline:home",
    link: "dashboard",
  },
  {
    title: "Users",
    icon: "heroicons-outline:users",
    link: "/users",
    child: [
      {
        childtitle: "Buyer",
        childlink: "/users/buyer",
      },
      {
        childtitle: "Seller",
        childlink: "/users/seller",
      },
      {
        childtitle: "Supplier",
        childlink: "/users/supplier",
      },
      {
        childtitle: "Create User",
        childlink: "/users/create",
      },
    ],
  },
  {
    title: "Request",
    icon: "heroicons-outline:folder",
    link: "/requests",
    child: [
      {
        childtitle: "Pending",
        childlink: "/requests/pending",
      },
      {
        childtitle: "Approved",
        childlink: "/requests/approved",
      },
      {
        childtitle: "Merged",
        childlink: "/requests/merged",
      },
      {
        childtitle: "Cancelled",
        childlink: "/requests/cancelled",
      },
      {
        childtitle: "Rejected",
        childlink: "/requests/rejected",
      },
      {
        childtitle: "Processed",
        childlink: "/requests/processed",
      }
    ],
  },


  {
    title: "Offers",
    icon: "heroicons-outline:folder",
    link: "/offers",
    child: [
      {
        childtitle: "Pending",
        childlink: "/offers/pending",
      },
      {
        childtitle: "Approved",
        childlink: "/offers/approved",
      },
      {
        childtitle: "Rejected",
        childlink: "/offers/rejected",
      },
      {
        childtitle: "Processed",
        childlink: "/offers/processed",
      }
    ],
  },

  {
    title: "Orders",
    icon: "heroicons-outline:shopping-cart",
    link: "/orders",
    child: [
      {
        childtitle: "All orders",
        childlink: "/orders/all",
      },
      {
        childtitle: "Pending orders",
        childlink: "/orders/pending",
      },
      {
        childtitle: "Proceed orders",
        childlink: "/orders/proceed",
      },
      {
        childtitle: "Delivered Orders",
        childlink: "/orders/delivered",
      },
      {
        childtitle: "Canceled Orders",
        childlink: "/orders/canceled",
      },
      {
        childtitle: "Payment failed",
        childlink: "/orders/payment-failed",
      },
      {
        childtitle: "Refunded orders",
        childlink: "/orders/refunded",
      },
    ],
  },
  {
    title: "Categories",
    icon: "heroicons-outline:folder",
    link: "/categories",
    child: [
      {
        childtitle: "All categories",
        childlink: "/categories",
      },
      {
        childtitle: "Premium Categories",
        childlink: "/categories/premium",
      },
      {
        childtitle: "Featured",
        childlink: "/categories/featured",
      },
      {
        childtitle: "Add new category",
        childlink: "/category/create",
      },
    ],
  },
  {
    title: "Subscriptions",
    icon: "heroicons-outline:gift",
    link: "/subscriptions",
    child: [
      {
        childtitle: "All Subscriptions",
        childlink: "/subscriptions",
      },
      {
        childtitle: "Active Plans",
        childlink: "/subscriptions/active",
      },
      {
        childtitle: "Featured Plans",
        childlink: "/subscriptions/featured",
      },
      {
        childtitle: "Create Plan",
        childlink: "/subscription/create",
      },
    ],
  },
  {
    title: "Employees",
    icon: "heroicons-outline:user-group",
    link: "#",
    child: [
      {
        childtitle: "All employees",
        childlink: "#",
      },
      {
        childtitle: "Roles",
        childlink: "#",
      },
      {
        childtitle: "Add new employee",
        childlink: "#",
      },
    ],
  },
  {
    title: "Statistic & Reports",
    icon: "heroicons-outline:chart-pie",
    link: "#",
  },
  {
    title: "Feedback and Disputes",
    icon: "heroicons-outline:chat-alt",
    link: "#",
  },
  {
    title: "Setting",
    icon: "heroicons-outline:cog",
    link: "#",
  },
];

export const topMenu = [
  {
    title: "Dashboard",
    icon: "heroicons-outline:home",
    link: "/app/home",
    child: [
      {
        childtitle: "Analytics Dashboard",
        childlink: "dashboard",
        childicon: "heroicons:presentation-chart-line",
      },
    ],
  },
];


export const colors = {
  primary: "#4669FA",
  secondary: "#A0AEC0",
  danger: "#F1595C",
  black: "#111112",
  warning: "#FA916B",
  info: "#0CE7FA",
  light: "#425466",
  success: "#50C793",
  "gray-f7": "#F7F8FC",
  dark: "#1E293B",
  "dark-gray": "#0F172A",
  gray: "#68768A",
  gray2: "#EEF1F9",
  "dark-light": "#CBD5E1",
};

