import React from "react";
import useFooterType from "@/hooks/useFooterType";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react";

const Footer = ({ className = "custom-class" }) => {
  const [footerType] = useFooterType();
  const footerclassName = () => {
    switch (footerType) {
      case "sticky":
        return "sticky bottom-0 z-[999]";
      case "static":
        return "static";
      case "hidden":
        return "hidden";
    }
  };
  return (
    <footer className={className + " " + footerclassName()}>
      <div className="site-footer px-6 bg-gray-200 dark:bg-slate-800 text-slate-500 dark:text-slate-300 py-4">
        <div className="grid md:grid-cols-3 grid-cols-1 md:gap-5">
          
         

          <div className="ltr:md:text-right rtl:md:text-end text-center text-sm">
            Hand-crafted & Made by{" "}
            <a
              href="#"
              target="_blank"
              className="text-primary-500 font-semibold"
            >
            Markt Zoom  
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
