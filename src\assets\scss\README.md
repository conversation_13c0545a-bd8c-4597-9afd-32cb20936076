# SASS Migration Plan

## Current Status

The project currently uses deprecated SASS `@import` rules that will be removed in Dart Sass 3.0.0.

## Migration Plan

1. We've created `_index.scss` files in each directory (components, layout, utility) that use the new `@forward` syntax.

2. For now, we're keeping the `@import` syntax in `app.scss` to avoid breaking changes.

3. When ready to migrate, you can update `app.scss` to use the new `@use` syntax:

```scss
// Replace all the @import statements with these three @use statements
@use "components";
@use "layout";
@use "utility";
```

## Benefits of Migration

1. Better encapsulation: Variables, mixins, and functions are local to the file by default
2. Explicit namespacing: Helps avoid naming conflicts
3. Future-proof: Ensures compatibility with Dart Sass 3.0.0 and beyond

## Resources

- [Sass @use Documentation](https://sass-lang.com/documentation/at-rules/use)
- [Sass @forward Documentation](https://sass-lang.com/documentation/at-rules/forward)
- [Sass Migration Tool](https://sass-lang.com/documentation/cli/migrator)
