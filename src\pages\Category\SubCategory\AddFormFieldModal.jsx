import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { Formik, Form, Field, FieldArray } from "formik";
import * as Yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Icon } from '@iconify/react';

const AddFormFieldModal = ({ showModal, setShowModal, subcategoryId, onSuccess }) => {
  const [postApi, { isLoading }] = usePostApiMutation();

  const validationSchema = Yup.object().shape({
    label_name: Yup.string()
      .required('Label name is required')
      .max(255, 'Label name must be less than 255 characters'),
    input_type: Yup.string()
      .required('Input type is required')
      .oneOf([
        'TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'DATE', 'TIME',
        'DATETIME', 'CHECKBOX', 'RADIO', 'SELECT', 'MULTISELECT', 'FILE',
        'PHONE', 'URL', 'COLOR', 'RANGE'
      ], 'Invalid input type'),
    label_subtitle: Yup.string()
      .max(500, 'Label subtitle must be less than 500 characters'),
    placeholder: Yup.string()
      .max(255, 'Placeholder must be less than 255 characters'),
    is_required: Yup.boolean(),
    options: Yup.array().when('input_type', {
      is: (type) => ['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(type),
      then: () => Yup.array()
        .min(1, 'At least one option is required for this input type')
        .of(Yup.string().required('Option cannot be empty')),
      otherwise: () => Yup.array()
    }),
    default_value: Yup.string(),
    validation_regex: Yup.string()
      .test('is-valid-regex', 'Invalid regex pattern', value => {
        if (!value) return true;
        try {
          new RegExp(value);
          return true;
        } catch (e) {
          return false;
        }
      }),
    min_value: Yup.number().integer('Must be an integer'),
    max_value: Yup.number().integer('Must be an integer')
      .test('is-greater-than-min', 'Max value must be greater than or equal to min value',
        function(value) {
          const { min_value } = this.parent;
          if (min_value === undefined || value === undefined) return true;
          return value >= min_value;
        }),
    min_length: Yup.number().integer('Must be an integer').min(0, 'Must be non-negative'),
    max_length: Yup.number().integer('Must be an integer').min(1, 'Must be positive')
      .test('is-greater-than-min', 'Max length must be greater than or equal to min length',
        function(value) {
          const { min_length } = this.parent;
          if (min_length === undefined || value === undefined) return true;
          return value >= min_length;
        }),

  });

  const initialValues = {
    label_name: '',
    input_type: '',
    label_subtitle: '',
    placeholder: '',
    is_required: false,
    options: [],
    default_value: '',
    validation_regex: '',
    min_value: '',
    max_value: '',
    min_length: '',
    max_length: '',

  };

  const handleSubmit = async (values, { resetForm, setErrors }) => {
    try {
      // Clean up empty values
      const formData = Object.entries(values).reduce((acc, [key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          if (key === 'options' && !['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(values.input_type)) {
            acc[key] = [];
          } else {
            acc[key] = value;
          }
        }
        return acc;
      }, {});

      const response = await postApi({
        end_point: `/admin/subcategories/${subcategoryId}/form-fields`,
        body: formData
      });

      if (response.error) {
        console.error('Error:', response.error);
        if (response.error.data && response.error.data.errors) {
          setErrors(response.error.data.errors);
        }
      } else {
        resetForm();
        setShowModal(false);
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Add Form Field"
      className="max-w-3xl"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, errors, touched, isSubmitting, setFieldValue }) => (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Label Name */}
              <div>
                <label htmlFor="label_name" className="block mb-1 font-medium">
                  Label Name <span className="text-red-500">*</span>
                </label>
                <Field
                  name="label_name"
                  type="text"
                  className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.label_name && touched.label_name ? 'border-red-500' : ''
                  }`}
                />
                {errors.label_name && touched.label_name && (
                  <div className="text-red-500 text-sm mt-1">{errors.label_name}</div>
                )}
              </div>

              {/* Input Type */}
              <div>
                <label htmlFor="input_type" className="block mb-1 font-medium">
                  Input Type <span className="text-red-500">*</span>
                </label>
                <Field
                  as="select"
                  name="input_type"
                  className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.input_type && touched.input_type ? 'border-red-500' : ''
                  }`}
                >
                  <option value="">Select Input Type</option>
                  <option value="TEXT">Text</option>
                  <option value="TEXTAREA">Text Area</option>
                  <option value="NUMBER">Number</option>
                  <option value="EMAIL">Email</option>
                  <option value="PASSWORD">Password</option>
                  <option value="DATE">Date</option>
                  <option value="TIME">Time</option>
                  <option value="DATETIME">Date & Time</option>
                  <option value="CHECKBOX">Checkbox</option>
                  <option value="RADIO">Radio</option>
                  <option value="SELECT">Select</option>
                  <option value="MULTISELECT">Multi Select</option>
                  <option value="FILE">File</option>
                  <option value="PHONE">Phone</option>
                  <option value="URL">URL</option>
                  <option value="COLOR">Color</option>
                  <option value="RANGE">Range</option>
                </Field>
                {errors.input_type && touched.input_type && (
                  <div className="text-red-500 text-sm mt-1">{errors.input_type}</div>
                )}
              </div>

              {/* Label Subtitle */}
              <div>
                <label htmlFor="label_subtitle" className="block mb-1 font-medium">
                  Label Subtitle
                </label>
                <Field
                  name="label_subtitle"
                  type="text"
                  className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.label_subtitle && touched.label_subtitle ? 'border-red-500' : ''
                  }`}
                />
                {errors.label_subtitle && touched.label_subtitle && (
                  <div className="text-red-500 text-sm mt-1">{errors.label_subtitle}</div>
                )}
              </div>

              {/* Placeholder */}
              <div>
                <label htmlFor="placeholder" className="block mb-1 font-medium">
                  Placeholder
                </label>
                <Field
                  name="placeholder"
                  type="text"
                  className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.placeholder && touched.placeholder ? 'border-red-500' : ''
                  }`}
                />
                {errors.placeholder && touched.placeholder && (
                  <div className="text-red-500 text-sm mt-1">{errors.placeholder}</div>
                )}
              </div>

              {/* Required Field */}
              <div className="flex items-center h-full pt-6">
                <label className="flex items-center cursor-pointer">
                  <Field
                    type="checkbox"
                    name="is_required"
                    className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-2 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-700">Required Field</span>
                </label>
              </div>


            </div>

            {/* Options for SELECT, RADIO, CHECKBOX, MULTISELECT */}
            {['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(values.input_type) && (
              <div className="mt-4 p-3 border border-dashed border-gray-300 rounded-lg">
                <label className="block mb-2 font-medium">
                  Options <span className="text-red-500">*</span>
                </label>
                <FieldArray name="options">
                  {({ push, remove }) => (
                    <div className="space-y-2">
                      {values.options.map((_, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Field
                            name={`options.${index}`}
                            type="text"
                            className={`flex-1 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                              errors.options?.[index] && touched.options?.[index] ? 'border-red-500' : ''
                            }`}
                            placeholder="Option value"
                          />
                          <button
                            type="button"
                            onClick={() => remove(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Icon icon="lucide:x" className="w-5 h-5" />
                          </button>
                        </div>
                      ))}
                      {errors.options && typeof errors.options === 'string' && (
                        <div className="text-red-500 text-sm mt-1">{errors.options}</div>
                      )}
                      <button
                        type="button"
                        onClick={() => push('')}
                        className="mt-2 flex items-center text-blue-600 hover:text-blue-800"
                      >
                        <Icon icon="lucide:plus" className="w-4 h-4 mr-1" />
                        Add Option
                      </button>
                    </div>
                  )}
                </FieldArray>
              </div>
            )}

            {/* Advanced Settings - Collapsible */}
            <div className="mt-4 border border-gray-200 rounded-lg">
              <details className="p-3">
                <summary className="font-medium cursor-pointer">Advanced Settings</summary>
                <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Default Value */}
                  <div>
                    <label htmlFor="default_value" className="block mb-1 font-medium">
                      Default Value
                    </label>
                    <Field
                      name="default_value"
                      type="text"
                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.default_value && touched.default_value ? 'border-red-500' : ''
                      }`}
                    />
                    {errors.default_value && touched.default_value && (
                      <div className="text-red-500 text-sm mt-1">{errors.default_value}</div>
                    )}
                  </div>

                  {/* Validation Regex */}
                  <div>
                    <label htmlFor="validation_regex" className="block mb-1 font-medium">
                      Validation Regex
                    </label>
                    <Field
                      name="validation_regex"
                      type="text"
                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.validation_regex && touched.validation_regex ? 'border-red-500' : ''
                      }`}
                    />
                    {errors.validation_regex && touched.validation_regex && (
                      <div className="text-red-500 text-sm mt-1">{errors.validation_regex}</div>
                    )}
                  </div>

                  {/* Min Value */}
                  <div>
                    <label htmlFor="min_value" className="block mb-1 font-medium">
                      Min Value
                    </label>
                    <Field
                      name="min_value"
                      type="number"
                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.min_value && touched.min_value ? 'border-red-500' : ''
                      }`}
                    />
                    {errors.min_value && touched.min_value && (
                      <div className="text-red-500 text-sm mt-1">{errors.min_value}</div>
                    )}
                  </div>

                  {/* Max Value */}
                  <div>
                    <label htmlFor="max_value" className="block mb-1 font-medium">
                      Max Value
                    </label>
                    <Field
                      name="max_value"
                      type="number"
                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.max_value && touched.max_value ? 'border-red-500' : ''
                      }`}
                    />
                    {errors.max_value && touched.max_value && (
                      <div className="text-red-500 text-sm mt-1">{errors.max_value}</div>
                    )}
                  </div>

                  {/* Min Length */}
                  <div>
                    <label htmlFor="min_length" className="block mb-1 font-medium">
                      Min Length
                    </label>
                    <Field
                      name="min_length"
                      type="number"
                      min="0"
                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.min_length && touched.min_length ? 'border-red-500' : ''
                      }`}
                    />
                    {errors.min_length && touched.min_length && (
                      <div className="text-red-500 text-sm mt-1">{errors.min_length}</div>
                    )}
                  </div>

                  {/* Max Length */}
                  <div>
                    <label htmlFor="max_length" className="block mb-1 font-medium">
                      Max Length
                    </label>
                    <Field
                      name="max_length"
                      type="number"
                      min="1"
                      className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.max_length && touched.max_length ? 'border-red-500' : ''
                      }`}
                    />
                    {errors.max_length && touched.max_length && (
                      <div className="text-red-500 text-sm mt-1">{errors.max_length}</div>
                    )}
                  </div>
                </div>
              </details>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                onClick={() => setShowModal(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isSubmitting || isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400"
              >
                {isSubmitting || isLoading ? 'Adding...' : 'Add Form Field'}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default AddFormFieldModal;
