import React, { useState, useEffect } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import Delete from "./Delete";

const SubscriptionPage = () => {
  const { status } = useParams();
  const navigate = useNavigate();
  const [apiParam, setApiParam] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  // Build API endpoint with filters
  const buildApiEndpoint = () => {
    let endpoint = '/admin/subscriptions';
    const params = new URLSearchParams();
    
    if (apiParam) {
      const urlParams = new URLSearchParams(apiParam.replace('?', ''));
      urlParams.forEach((value, key) => {
        params.append(key, value);
      });
    }

    // Add status-based filters
    if (status === 'active') {
      params.set('is_active', 'true');
    } else if (status === 'featured') {
      params.set('is_featured', 'true');
    }

    const queryString = params.toString();
    return queryString ? `${endpoint}?${queryString}` : endpoint;
  };

  const { data: subscriptions, isLoading, isFetching } = useGetApiQuery(buildApiEndpoint());

  const tableData = subscriptions?.data?.map((item) => ({
    id: item.id,
    name: (
      <Link 
        to={`/subscription/${item.id}`} 
        className="hover:text-primary-500 hover:underline font-medium"
      >
        {item.name}
      </Link>
    ),
    price: (
      <span className="font-semibold text-green-600">
        ${parseFloat(item.price).toFixed(2)}
      </span>
    ),
    duration: `${item.duration_days} days`,
    user_type: (
      <Badge
        className={
          item.user_type === 'BUYER' 
            ? 'bg-blue-100 text-blue-800'
            : item.user_type === 'SELLER'
            ? 'bg-purple-100 text-purple-800'
            : 'bg-gray-100 text-gray-800'
        }
      >
        {item.user_type}
      </Badge>
    ),
    status: (
      <Badge
        className={
          item.is_active
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }
      >
        {item.is_active ? 'Active' : 'Inactive'}
      </Badge>
    ),
    featured: (
      <Badge
        className={
          item.is_featured
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-gray-100 text-gray-800'
        }
      >
        {item.is_featured ? 'Featured' : 'Regular'}
      </Badge>
    ),
    subscribers: item._count?.user_subscriptions || 0,
  })) || [];

  const changePage = (val) => {
    setApiParam(val ? `?page=${val}` : '');
  };

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/subscription/edit/${subscriptions.data[val].id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons:trash",
      onClick: (val) => {
        setDeleteData(subscriptions.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const columns = [
    {
      label: "Plan Name",
      field: "name",
    },
    {
      label: "Price",
      field: "price",
    },
    {
      label: "Duration",
      field: "duration",
    },
    {
      label: "User Type",
      field: "user_type",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Featured",
      field: "featured",
    },
    {
      label: "Subscribers",
      field: "subscribers",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const getPageTitle = () => {
    switch (status) {
      case 'active':
        return 'Active Subscription Plans';
      case 'featured':
        return 'Featured Subscription Plans';
      default:
        return 'All Subscription Plans';
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <BasicTablePage
        loading={isLoading || isFetching}
        title={getPageTitle()}
        CreateUrl="/subscription/create"
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        currentPage={subscriptions?.meta?.page}
        setFilter={setApiParam}
        totalPages={subscriptions?.meta?.totalPages}
      />

      {showDeleteModal && (
        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      )}
    </div>
  );
};

export default SubscriptionPage;
