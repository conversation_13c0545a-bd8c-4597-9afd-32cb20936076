import { createSlice } from "@reduxjs/toolkit";
import { clearAuthData } from "@/utils/authUtils";

export const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: null,
    isAuth: false,
    token: null,
  },
  reducers: {
    setUser: (state, action) => {
      localStorage.setItem("user", JSON.stringify(action.payload));
      state.user = action.payload;
      state.isAuth = true;
    },
    setToken: (state, action) => {
      state.token = action.payload;
    },
    logOut: (state) => {
      // Clear all auth data from cookies and localStorage
      clearAuthData();

      // Reset Redux state
      state.user = null;
      state.isAuth = false;
      state.token = null;
    },
  },
});

export const { setUser, setToken, logOut } = authSlice.actions;
export default authSlice.reducer;
