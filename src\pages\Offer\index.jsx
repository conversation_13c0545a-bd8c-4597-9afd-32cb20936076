import React, { useState, useMemo } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Link } from "react-router-dom";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Loading from "@/components/Loading";
import Badge from "@/components/ui/Badge";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";

const OfferPage = () => {
  const navigate = useNavigate();
  const { statusParam } = useParams();
  const location = useLocation();
  const queryParam = location.search || "";

  const [apiParam, setApiParam] = useState(queryParam);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState(null);

  const {
    data: offersResponse,
    isLoading,
    isError,
    isFetching
  } = useGetApiQuery(`/admin/offers${statusParam ? `?status=${statusParam}` : ""}${apiParam}`);

  const handleOfferClick = (id) => {
    navigate(`/offer/${id}`);
  };

  const offers = offersResponse?.data || [];

  const tableData = offers.map((item) => {
    return {
      id: item.id,
      seller: (
        <div className="flex items-center gap-2">
          {item.seller?.profile_picture_url && (
            <img
              src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.seller.profile_picture_url}`}
              className="h-8 w-8 rounded-full"
            />
          )}
          <span>{`${item.seller?.first_name || ''} ${item.seller?.last_name || ''}`}</span>
        </div>
      ),
      request: (
        <Link
          to={`/request/${item.request?.id}`}
          className="hover:text-primary-500 hover:underline"
        >
          {item.request?.title || 'N/A'}
        </Link>
      ),
      price: `$${item.price}`,
      delivery_time: `${item.delivery_time} days`,
      created_at: new Date(item.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }),
      status: (
        <Badge
          className={
            item.status === "Approved"
              ? "bg-success-500 text-white"
              : item.status === "Rejected"
              ? "bg-danger-500 text-white"
              : item.status === "Pending"
              ? "bg-warning-500 text-white"
              : item.status === "Merged"
              ? "bg-info-500 text-white"
              : "bg-primary-500 text-white"
          }
        >
          {item.status}
        </Badge>
      ),
    };
  });

  const changePage = (val) => {
    setApiParam(val);
  };

  const columns = [
    {
      label: "Seller",
      field: "seller",
    },
    {
      label: "Request",
      field: "request",
    },
    {
      label: "Price",
      field: "price",
    },
    {
      label: "Delivery Time",
      field: "delivery_time",
    },
    {
      label: "Created At",
      field: "created_at",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const actions = [
    {
      name: "View",
      icon: "heroicons:eye",
      onClick: (val) => {
        handleOfferClick(offers[val].id);
      },
    },
    {
      name: "Approve",
      icon: "heroicons:check-square",
      onClick: (val) => {
        setSelectedOffer(offers[val]);
        setShowApproveModal(true);
      },
      hidden: statusParam === 'approved'
    },
    {
      name: "Reject",
      icon: "heroicons:close-square",
      onClick: (val) => {
        setSelectedOffer(offers[val]);
        setShowRejectModal(true);
      },
      hidden: statusParam === 'rejected'
    },
  ];

  return (
    <div className="flex flex-col gap-5">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">
          {statusParam ? `Offers ${statusParam.charAt(0).toUpperCase() + statusParam.slice(1)}` : 'Offers'}
        </h2>
        {statusParam && (
          <Link to="/offers" className="btn btn-primary">
            All Offers
          </Link>
        )}
      </div>

      {isLoading || isFetching ? (
        <Loading />
      ) : isError ? (
        <p className="text-red-500">Something went wrong</p>
      ) : (
        <BasicTablePage
          title={`${statusParam ? statusParam.charAt(0).toUpperCase() + statusParam.slice(1) : ''} Offers`}
          columns={columns}
          actions={actions}
          data={tableData}
          changePage={changePage}
          currentPage={offersResponse?.meta?.page || 1}
          setFilter={setApiParam}
          totalPages={offersResponse?.meta?.totalPages || 1}
          hideCreateButton={true}
        />
      )}

      {showApproveModal && (
        <ApproveModal
          showApproveModal={showApproveModal}
          setShowApproveModal={setShowApproveModal}
          data={selectedOffer}
        />
      )}

      {showRejectModal && (
        <RejectModal
          showRejectModal={showRejectModal}
          setShowRejectModal={setShowRejectModal}
          data={selectedOffer}
        />
      )}
    </div>
  );
};

export default OfferPage;
