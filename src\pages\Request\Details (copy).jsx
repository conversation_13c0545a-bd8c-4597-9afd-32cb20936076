import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import Button from "@/components/ui/Button";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";
import Sellers from "./Sellers";
import { toast } from "react-toastify";
// File type icons
import {
  FaFilePdf,
  FaFileWord,
  FaFileExcel,
  FaFilePowerpoint,
  FaFileAlt,
  FaFileImage,
  FaFileVideo,
  FaFileAudio,
  FaFileCsv,
  FaFileArchive,
  FaFileCode
} from "react-icons/fa";

const RequestDetailsPage = () => {
    const { id } = useParams();
    const [showApproveModal, setShowApproveModal] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const { data: responseData, isLoading, isError } = useGetApiQuery(`admin/requests/${id}`);

    if (isLoading) return <Loading />;
    if (isError) return <div className="text-red-500">Error fetching data</div>;

    const request = responseData.data;
    const attachments = request.request_attachments || [];
    const statusHistory = request.request_statuses || [];

    // Function to check if a file is an image based on extension
    const isImageFile = (filePath) => {
        if (!filePath) return false;
        const extension = filePath.split('.').pop().toLowerCase();
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
    };

    // Function to get file type icon based on extension
    const getFileIcon = (filePath) => {
        if (!filePath) return <FaFileAlt size={40} />;

        const extension = filePath.split('.').pop().toLowerCase();

        switch (extension) {
            case 'pdf':
                return <FaFilePdf size={40} color="#e74c3c" />;
            case 'doc':
            case 'docx':
                return <FaFileWord size={40} color="#2980b9" />;
            case 'xls':
            case 'xlsx':
                return <FaFileExcel size={40} color="#27ae60" />;
            case 'ppt':
            case 'pptx':
                return <FaFilePowerpoint size={40} color="#e67e22" />;
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'webp':
            case 'svg':
                return <FaFileImage size={40} color="#3498db" />;
            case 'mp4':
            case 'avi':
            case 'mov':
            case 'wmv':
                return <FaFileVideo size={40} color="#9b59b6" />;
            case 'mp3':
            case 'wav':
            case 'ogg':
                return <FaFileAudio size={40} color="#f39c12" />;
            case 'csv':
                return <FaFileCsv size={40} color="#16a085" />;
            case 'zip':
            case 'rar':
            case '7z':
                return <FaFileArchive size={40} color="#7f8c8d" />;
            case 'html':
            case 'css':
            case 'js':
            case 'json':
            case 'xml':
                return <FaFileCode size={40} color="#2c3e50" />;
            default:
                return <FaFileAlt size={40} color="#95a5a6" />;
        }
    };

    // Filter image attachments and non-image attachments
    const imageAttachments = attachments.filter(att => isImageFile(att.file_path));
    const documentAttachments = attachments.filter(att => !isImageFile(att.file_path));

    // Format date and time for status history
    const formatDateTime = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return 'Invalid date';
        }
    };

    // Get status badge color
    const getStatusColor = (status) => {
        if (!status) return 'gray';

        switch (status.toLowerCase()) {
            case 'active':
            case 'approved':
            case 'completed':
                return 'success';

            case 'inactive':
            case 'in progress':
            case 'processing':
                return 'warning';

            case 'suspended':
            case 'rejected':
            case 'cancelled':
                return 'danger';

            case 'pending':
                return 'info';

            default:
                return 'gray';
        }
    };

    // Get the preview image based on selected index or fallback to default
    const previewImage = imageAttachments.length > 0 && imageAttachments[selectedImageIndex]?.file_path
        ? `${import.meta.env.VITE_ASSET_HOST_URL}${imageAttachments[selectedImageIndex].file_path}`
        : "/placeholder.jpg"; // fallback image

    // Handle thumbnail click
    const handleThumbnailClick = (index) => {
        setSelectedImageIndex(index);
    };

    // Handle document click
    const handleDocumentClick = (filePath) => {
        if (!filePath) return;

        const fileUrl = `${import.meta.env.VITE_ASSET_HOST_URL}${filePath}`;
        window.open(fileUrl, '_blank');
    };

    return (
        <div className="">
            {/* Breadcrumb */}
            <div className="mb-4 text-sm text-gray-500 flex items-center space-x-2">
                <Link to="/" className="hover:underline">🏠 Requests</Link>
                <span>/</span>
                <span className="text-gray-800 font-medium">{request.title }</span>
            </div>

            {/* Main Card */}
            <div className="bg-white rounded-xl shadow-md p-6 grid md:grid-cols-2 gap-6">
                {/* Left Image + Thumbnails */}
                <div>
                    {/* Main Preview Area */}
                    {imageAttachments.length > 0 ? (
                        <img
                            src={previewImage}
                            alt="Preview"
                            className="w-full h-80 object-cover rounded-lg border"
                        />
                    ) : (
                        <div className="w-full h-80 flex items-center justify-center bg-gray-100 rounded-lg border">
                            <p className="text-gray-500">No image attachments available</p>
                        </div>
                    )}

                    {/* Image Thumbnails Section */}
                    {imageAttachments.length > 0 && (
                        <div className="mt-4">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">Images</h3>
                            <div className="flex gap-2 overflow-x-auto pb-2">
                                {imageAttachments.map((att, index) => (
                                    <img
                                        key={att.id}
                                        src={`${import.meta.env.VITE_ASSET_HOST_URL}${att.file_path}`}
                                        alt={`Thumbnail ${index + 1}`}
                                        className={`w-20 h-20 object-cover rounded-md border cursor-pointer transition ${
                                            selectedImageIndex === index
                                                ? 'border-2 border-blue-500 scale-105'
                                                : 'hover:scale-105 hover:border-gray-400'
                                        }`}
                                        onClick={() => handleThumbnailClick(index)}
                                    />
                                ))}
                                {(() => {
                                    const filePath = request.file;
                                    const fileName = filePath.split('/').pop();
                                    const fileExtension = fileName.split('.').pop().toUpperCase();

                                    return (
                                        <div
                                            className="flex flex-col items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition w-32"
                                            onClick={() => handleDocumentClick(filePath)}
                                            title={`Click to open ${fileName} (${fileExtension})`}
                                        >
                                            {getFileIcon(filePath)}
                                        </div>
                                    );
                                })()}
                            </div>
                        </div>
                    )}



                    {/* Document Attachments Section */}
                    {documentAttachments.length > 0 && (
                        <div className="mt-4">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">Additional Documents</h3>
                            <div className="flex flex-wrap gap-3">
                                {documentAttachments.map((doc) => {
                                    const fileName = doc.file_path.split('/').pop();
                                    const fileExtension = fileName.split('.').pop().toUpperCase();

                                    return (
                                        <div
                                            key={doc.id}
                                            className="flex flex-col items-center p-2 border rounded-md cursor-pointer hover:bg-gray-50 transition w-24"
                                            onClick={() => handleDocumentClick(doc.file_path)}
                                            title={`Click to open ${fileName}`}
                                        >
                                            {getFileIcon(doc.file_path)}
                                            <span className="text-xs font-medium text-gray-500 mt-1">{fileExtension}</span>
                                            <span className="text-xs text-gray-500 truncate w-full text-center">
                                                {fileName.length > 12 ? fileName.substring(0, 10) + '...' : fileName}
                                            </span>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                </div>

                {/* Right Details */}
                <div className="space-y-3">
                    <div className="flex justify-between items-start">
                        <h2 className="text-2xl font-bold text-gray-800">{request.title || "Box Delivery Service"}</h2>
                        <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-${getStatusColor(request.status)}-100 text-${getStatusColor(request.status)}-800`}>
                            {request.status}
                        </span>
                    </div>
                    <div className="text-sm text-gray-500 space-x-4">
                        <Link to={`/category/${request?.category?.id}`}>{request?.category?.title }</Link> -
                        <span >{request?.sub_category?.title }</span>
                    </div>

                    <div className="text-xl font-semibold text-gray-700">
                        ${request.budget_max} - ${request.budget_min}
                    </div>
                    <div className="text-sm text-gray-600">{request.quantity} Unit</div>
                    <div className="text-sm text-gray-600">
                    {new Date(request.deadline).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
                      </div>

                    <div>
                        <p className="text-gray-800 font-semibold">Short Description:</p>
                        <p className="text-gray-600">{request.short_description || "N/A"}</p>
                    </div>

                    <div>
                        <p className="text-gray-800 font-semibold">Details Descriptions:</p>
                        <p className="text-gray-600 whitespace-pre-line">{request.description || "N/A"}</p>
                    </div>

                    <div>
                        <p className="text-gray-800 font-semibold">Additional Criteria:</p>
                        <p className="text-gray-600">{request.additional_info || "N/A"}</p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-wrap gap-3 mt-6">
                        {request.status?.toLowerCase() !== 'approved' && (
                            <Button
                                onClick={() => setShowApproveModal(true)}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                                Approve
                            </Button>
                        )}
                        <Button
                            onClick={() => setShowRejectModal(true)}
                            className="bg-red-500 hover:bg-red-600 text-white"
                        >
                            Reject
                        </Button>
                        <Button
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                        >
                            Modify
                        </Button>
                    </div>
                </div>
            </div>

            {/* Status History Section */}
            {statusHistory.length > 0 && (
                <div className="mt-6 bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">Status History</h2>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date & Time
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Previous Status
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Updated By
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Reason
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {statusHistory.map((history) => (
                                    <tr key={history.id}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {formatDateTime(history.created_at)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.status)}-100 text-${getStatusColor(history.status)}-800`}>
                                                {history.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            {history.previous_status ? (
                                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.previous_status)}-100 text-${getStatusColor(history.previous_status)}-800`}>
                                                    {history.previous_status}
                                                </span>
                                            ) : (
                                                <span className="text-gray-400">-</span>
                                            )}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {history.updated_by_user ? (
                                                <div className="flex items-center">
                                                    <div className="ml-1">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {history.updated_by_user.first_name} {history.updated_by_user.last_name}
                                                        </div>
                                                        <div className="text-xs text-gray-500">
                                                            {history.updated_by_user.email}
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                <span className="text-gray-400">-</span>
                                            )}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {history.reason || <span className="text-gray-400">-</span>}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {/* Assigned Sellers Section */}
            {request.assigned_sellers && request.assigned_sellers.length > 0 && (
                <div className="mt-6 bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">Assigned Sellers</h2>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Seller
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Assigned By
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Assigned At
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Notes
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {request.assigned_sellers.map((assignment) => (
                                    <tr key={assignment.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 flex-shrink-0">
                                                    <img
                                                        className="h-10 w-10 rounded-full object-cover"
                                                        src={assignment.seller.profile_picture_url
                                                            ? `${import.meta.env.VITE_ASSET_HOST_URL}${assignment.seller.profile_picture_url}`
                                                            : '/images/avatar/default-avatar.png'
                                                        }
                                                        alt={`${assignment.seller.first_name} ${assignment.seller.last_name}`}
                                                    />
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {assignment.seller.first_name} {assignment.seller.last_name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {assignment.seller.email}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                assignment.status === 'Accepted'
                                                    ? 'bg-green-100 text-green-800'
                                                    : assignment.status === 'Rejected'
                                                        ? 'bg-red-100 text-red-800'
                                                        : 'bg-yellow-100 text-yellow-800'
                                            }`}>
                                                {assignment.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                {assignment.assigner.first_name} {assignment.assigner.last_name}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {new Date(assignment.assigned_at).toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 text-sm text-gray-500">
                                            {assignment.notes || <span className="text-gray-400">-</span>}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {/* Seller Assignment Component - Only show if request is approved and no sellers assigned yet */}
            {request.status?.toLowerCase() === 'approved' &&
            //  (!request.assigned_sellers || request.assigned_sellers.length === 0) && (
                <Sellers
                    requestId={request.id}
                />
            // )
            }

            {/* Modals */}
            {showApproveModal && (
                <ApproveModal showApproveModal={showApproveModal} setShowApproveModal={setShowApproveModal} data={request} />
            )}
            {showRejectModal && (
                <RejectModal isOpen={showRejectModal} onClose={() => setShowRejectModal(false)} request={request} />
            )}
        </div>
    );
};

export default RequestDetailsPage;
