import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const Delete = ({ showDeleteModal, setShowDeleteModal, data }) => {
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  const onSubmit = async () => {
    try {
      const response = await deleteApi({
        end_point: '/admin/subscriptions/' + data?.id,
        body: {}
      });
      
      if (response.error) {
        toast.error(response.error?.data?.message || 'Failed to delete subscription plan');
      } else {
        setShowDeleteModal(false);
      }
    } catch (error) {
      toast.error('An error occurred while deleting the subscription plan');
      console.error('Delete error:', error);
    }
  };

  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title="Delete Subscription Plan"
      className="max-w-md"
      centered
    >
      <div className="py-4">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
              />
            </svg>
          </div>
          
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Delete Subscription Plan
          </h3>
          
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Are you sure you want to delete the subscription plan "{data?.name}"? 
            This action cannot be undone and will affect all users currently subscribed to this plan.
          </p>
        </div>
        
        <div className="flex justify-center gap-3 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowDeleteModal(false)}
            className="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50"
            disabled={isLoading}
          >
            Cancel
          </Button>
          
          <Button
            type="button"
            onClick={onSubmit}
            isLoading={isLoading}
            className="px-4 py-2 bg-red-600 text-white hover:bg-red-700"
            disabled={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Delete Plan'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default Delete;
