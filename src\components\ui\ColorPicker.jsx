import React, { useState } from "react";
import { HexColorPicker } from "react-colorful";

const ColorPicker = ({ value, onChange, label }) => {
  const [isOpen, setIsOpen] = useState(false);

  const togglePicker = () => setIsOpen(!isOpen);

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
        {label}
      </label>
      <div className="relative">
        {/* Color Display */}
        <div
          className="w-full h-10 rounded-md border border-slate-300 cursor-pointer flex items-center px-3 hover:border-slate-400 transition-colors"
          onClick={togglePicker}
        >
          <div
            className="w-6 h-6 rounded-md mr-2"
            style={{ backgroundColor: value }}
          />
          <span className="text-sm text-slate-600">{value}</span>
        </div>

        {/* Color Picker */}
        {isOpen && (
          <div className="absolute z-50 mt-2 bg-white shadow-lg rounded-lg p-2">
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            <HexColorPicker
              color={value}
              onChange={onChange}
              className="relative z-50"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ColorPicker;
