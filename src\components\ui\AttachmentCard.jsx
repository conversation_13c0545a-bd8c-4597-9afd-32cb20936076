import React from "react";
import { FileText, Image as ImageIcon, File, Download } from "lucide-react";

const getFileIcon = (type) => {
    if (type.includes("image")) return <ImageIcon className="h-6 w-6 text-blue-500" />;
    if (type.includes("pdf") || type.includes("text") || type.includes("document")) 
        return <FileText className="h-6 w-6 text-red-500" />;
    return <File className="h-6 w-6 text-gray-500" />;
};

const isImageFile = (url) => {
    if (!url) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
};

const AttachmentCard = ({ name, type, size, url }) => {
    const formatSize = (bytes) => {
        if (bytes >= 1e6) return (bytes / 1e6).toFixed(1) + " MB";
        if (bytes >= 1e3) return (bytes / 1e3).toFixed(1) + " KB";
        return bytes + " B";
    };

    return (
        <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition">
            <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                    {getFileIcon(type)}
                </div>
                <div className="flex-grow">
                    <p className="text-sm font-medium text-gray-800 truncate">{name}</p>
                    <p className="text-xs text-gray-500">{formatSize(size)}</p>
                </div>
                <a 
                    href={url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline flex items-center"
                    download
                >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                </a>
            </div>
            
            {isImageFile(url) && (
                <div className="mt-3 border-t pt-3">
                    <img 
                        src={url} 
                        alt={name} 
                        className="max-w-full h-auto rounded-md shadow-sm max-h-60 object-contain"
                        onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = "data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22200%22%20height%3D%22200%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20200%20200%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_18d6b5e1e8b%20text%20%7B%20fill%3A%23AAAAAA%3Bfont-weight%3Abold%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_18d6b5e1e8b%22%3E%3Crect%20width%3D%22200%22%20height%3D%22200%22%20fill%3D%22%23EEEEEE%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2274.0859375%22%20y%3D%22104.5%22%3EImage%20preview%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E";
                        }}
                    />
                </div>
            )}
        </div>
    );
};

export default AttachmentCard;