import React from "react";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";

const StatusHistory = ({ statusHistory }) => {
    if (!statusHistory?.length) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="text-center py-8">
                    <Icon 
                        icon="heroicons-outline:clock" 
                        className="mx-auto h-12 w-12 text-gray-400"
                    />
                    <h3 className="mt-2 text-lg font-medium text-gray-900">No Status History</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        This request doesn't have any status changes yet.
                    </p>
                </div>
            </div>
        );
    }

    const getStatusVariant = (status) => {
        switch (status?.toLowerCase()) {
            case 'approved': return 'success';
            case 'rejected': return 'danger';
            case 'pending': return 'warning';
            case 'processing': return 'primary';
            case 'completed': return 'indigo';
            default: return 'gray';
        }
    };

    const getStatusIcon = (status) => {
        switch (status?.toLowerCase()) {
            case 'approved': return "heroicons-outline:check-circle";
            case 'rejected': return "heroicons-outline:x-circle";
            case 'pending': return "heroicons-outline:clock";
            case 'processing': return "heroicons-outline:cog";
            case 'completed': return "heroicons-outline:badge-check";
            default: return "heroicons-outline:information-circle";
        }
    };

    return (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Icon icon="heroicons-outline:clock" className="mr-2 text-gray-500" />
                    Status Timeline
                </h2>
            </div>

            <div className="p-6">
                <div className="relative">
                    {/* Vertical line */}
                    <div className="absolute left-5 top-0 h-full w-0.5 bg-gray-200"></div>

                    <div className="space-y-6">
                        {[...statusHistory]
                            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                            .map((history, index) => (
                                <div key={history.id} className="relative pl-10">
                                    {/* Status indicator */}
                                    <div className={`absolute left-0 top-1 w-6 h-6 rounded-full flex items-center justify-center border-2 border-${getStatusVariant(history.status)}-500 bg-white z-10`}>
                                        <Icon
                                            icon={getStatusIcon(history.status)}
                                            className={`h-3 w-3 text-${getStatusVariant(history.status)}-600`}
                                        />
                                    </div>

                                    {/* Status card */}
                                    <div className="bg-gray-50 rounded-lg p-4 shadow-xs border border-gray-200">
                                        <div className="flex justify-between items-start">
                                            <div className="flex items-center space-x-2">
                                                <Badge variant={getStatusVariant(history.status)}>
                                                    {history.status}
                                                </Badge>
                                                {history.previous_status && (
                                                    <>
                                                        <Icon icon="heroicons-outline:arrow-right" className="h-4 w-4 text-gray-400" />
                                                        <span className="text-sm text-gray-500">
                                                            {history.previous_status}
                                                        </span>
                                                    </>
                                                )}
                                            </div>
                                            <span className="text-xs text-gray-500 whitespace-nowrap">
                                                {history.created_at}
                                            </span>
                                        </div>

                                        {history.updated_by_user && (
                                            <div className="mt-3 flex items-center">
                                                <div className="flex-shrink-0">
                                                    <img
                                                        className="h-8 w-8 rounded-full"
                                                        src={history.updated_by_user.profile_picture_url || 
                                                            `https://ui-avatars.com/api/?name=${encodeURIComponent(history.updated_by_user.first_name + ' ' + history.updated_by_user.last_name)}`}
                                                        alt={`${history.updated_by_user.first_name} ${history.updated_by_user.last_name}`}
                                                    />
                                                </div>
                                                <div className="ml-3">
                                                    <p className="text-sm font-medium text-gray-900">
                                                        {history.updated_by_user.first_name} {history.updated_by_user.last_name}
                                                    </p>
                                                    <p className="text-xs text-gray-500">
                                                        {history.updated_by_user.email}
                                                    </p>
                                                </div>
                                            </div>
                                        )}

                                        {history.reason && (
                                            <div className="mt-3 pt-3 border-t border-gray-200">
                                                <p className="text-xs font-medium text-gray-500 mb-1">REASON</p>
                                                <p className="text-sm text-gray-700">
                                                    {history.reason}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StatusHistory;