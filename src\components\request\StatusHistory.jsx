import React from "react";
import { formatDateTime, getStatusColor } from "@/utils/statusHelpers";
import Icon from "@/components/ui/Icon";

const StatusHistory = ({ statusHistory }) => {
    if (!statusHistory?.length) return null;

    // Reverse the array to show newest status at the top
    const sortedHistory = [...statusHistory].sort((a, b) =>
        new Date(b.created_at) - new Date(a.created_at)
    );

    // Get status icon based on status
    const getStatusIcon = (status) => {
        if (!status) return "heroicons-outline:question-mark-circle";

        switch (status.toLowerCase()) {
            case 'approved':
                return "heroicons-outline:check-circle";
            case 'rejected':
                return "heroicons-outline:x-circle";
            case 'pending':
                return "heroicons-outline:clock";
            case 'processing':
                return "heroicons-outline:cog";
            case 'completed':
                return "heroicons-outline:badge-check";
            default:
                return "heroicons-outline:information-circle";
        }
    };

    return (
        <div className="bg-white rounded-xl shadow-md p-6 h-full">
            <h2 className="text-xl font-bold text-gray-800 mb-6">Status Timeline</h2>

            <div className="relative">
                {/* Vertical line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                {/* Status items */}
                <div className="space-y-8">
                    {sortedHistory.map((history) => (
                        <div key={history.id} className="relative pl-12">
                            {/* Status dot */}
                            <div className={`absolute left-0 w-8 h-8 rounded-full flex items-center justify-center bg-${getStatusColor(history.status)}-100 border-2 border-${getStatusColor(history.status)}-500 z-10`}>
                                <Icon
                                    icon={getStatusIcon(history.status)}
                                    className={`h-4 w-4 text-${getStatusColor(history.status)}-600`}
                                />
                            </div>

                            {/* Status content */}
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                                <div className="flex justify-between items-start mb-2">
                                    <div>
                                        <span className={`px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(history.status)}-100 text-${getStatusColor(history.status)}-800`}>
                                            {history.status}
                                        </span>
                                        {history.previous_status && (
                                            <span className="text-gray-500 text-xs ml-2">
                                                from <span className={`px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(history.previous_status)}-100 text-${getStatusColor(history.previous_status)}-800`}>
                                                    {history.previous_status}
                                                </span>
                                            </span>
                                        )}
                                    </div>
                                    <span className="text-xs text-gray-500">
                                        {formatDateTime(history.created_at)}
                                    </span>
                                </div>

                                {history.updated_by_user && (
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons-outline:user" className="h-4 w-4 text-gray-500 mr-1" />
                                        <span className="text-sm font-medium text-gray-700">
                                            {history.updated_by_user.first_name} {history.updated_by_user.last_name}
                                        </span>
                                        <span className="text-xs text-gray-500 ml-2">
                                            ({history.updated_by_user.email})
                                        </span>
                                    </div>
                                )}

                                {history.reason && (
                                    <div className="mt-2 text-sm text-gray-600 bg-white p-3 rounded border border-gray-100">
                                        <div className="flex items-center mb-1">
                                            <Icon icon="heroicons-outline:annotation" className="h-4 w-4 text-gray-500 mr-1" />
                                            <span className="font-medium text-gray-700">Reason:</span>
                                        </div>
                                        <p className="pl-5">{history.reason}</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default StatusHistory;