import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useUpdateApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import InputField from '@/components/ui/InputField';
import TextareaField from '@/components/ui/TextareaField';
import DynamicFormField from '@/components/ui/DynamicFormField';
import { toast } from 'react-toastify';
import Loading from '@/components/Loading';

const EditRequestModal = ({ showModal, setShowModal, requestData }) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // Fetch subcategory form fields
  const { 
    data: subcategoryData, 
    isLoading: isLoadingSubcategory,
    isError: isSubcategoryError 
  } = useGetApiQuery(
    requestData?.sub_category_id ? `/admin/subcategories/${requestData.sub_category_id}` : null,
    { skip: !requestData?.sub_category_id }
  );

  const validationSchema = Yup.object().shape({
    title: Yup.string()
      .required('Title is required')
      .min(3, 'Title must be at least 3 characters')
      .max(200, 'Title must not exceed 200 characters'),
    short_description: Yup.string()
      .max(500, 'Short description must not exceed 500 characters'),
    description: Yup.string()
      .max(2000, 'Description must not exceed 2000 characters'),
    quantity: Yup.number()
      .positive('Quantity must be positive')
      .integer('Quantity must be an integer'),
    budget_min: Yup.number()
      .min(0, 'Minimum budget must be non-negative'),
    budget_max: Yup.number()
      .min(0, 'Maximum budget must be non-negative')
      .test('budget-range', 'Maximum budget must be greater than or equal to minimum budget', function(value) {
        const { budget_min } = this.parent;
        if (value && budget_min && parseFloat(value) < parseFloat(budget_min)) {
          return false;
        }
        return true;
      }),
    deadline: Yup.date(),
    urgency: Yup.string()
      .oneOf(['Low', 'Normal', 'High', 'Urgent'], 'Invalid urgency level'),
    request_type: Yup.string()
      .oneOf(['General', 'Service', 'Product', 'Consultation'], 'Invalid request type'),
    location: Yup.string()
      .max(200, 'Location must not exceed 200 characters'),
    additional_info: Yup.string()
      .max(1000, 'Additional info must not exceed 1000 characters'),
  });

  const getInitialValues = () => {
    if (!requestData) return {};

    return {
      title: requestData.title || '',
      short_description: requestData.short_description || '',
      description: requestData.description || '',
      quantity: requestData.quantity || 1,
      budget_min: requestData.budget_min || '',
      budget_max: requestData.budget_max || '',
      deadline: requestData.deadline ? new Date(requestData.deadline).toISOString().split('T')[0] : '',
      urgency: requestData.urgency || 'Normal',
      request_type: requestData.request_type || 'General',
      location: requestData.location || '',
      additional_info: requestData.additional_info || '',
      custom_fields: requestData.custom_fields || {}
    };
  };

  const handleSubmit = async (values, { setErrors }) => {
    setSubmitAttempted(true);
    
    try {
      const payload = {
        title: values.title,
        short_description: values.short_description || null,
        description: values.description || null,
        quantity: parseInt(values.quantity) || 1,
        budget_min: parseFloat(values.budget_min) || null,
        budget_max: parseFloat(values.budget_max) || null,
        deadline: values.deadline || null,
        urgency: values.urgency,
        request_type: values.request_type,
        location: values.location || null,
        additional_info: values.additional_info || null,
        custom_fields: values.custom_fields || {}
      };

      const response = await updateApi({
        end_point: `/admin/requests/${requestData.id}`,
        body: payload
      });

      if (response.error) {
        console.error('Error:', response.error);
        if (response.error.data && response.error.data.errors) {
          setErrors(response.error.data.errors);
        }
        toast.error(response.error.data?.message || 'Failed to update request');
      } else {
        toast.success('Request updated successfully');
        setShowModal(false);
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('An error occurred while updating the request');
    }
  };

  if (isLoadingSubcategory) {
    return (
      <Modal
        title="Edit Request"
        activeModal={showModal}
        onClose={() => setShowModal(false)}
        centered
        className="max-w-4xl"
      >
        <div className="flex justify-center items-center py-8">
          <Loading />
        </div>
      </Modal>
    );
  }

  if (isSubcategoryError) {
    return (
      <Modal
        title="Edit Request"
        activeModal={showModal}
        onClose={() => setShowModal(false)}
        centered
        className="max-w-4xl"
      >
        <div className="text-center py-8">
          <p className="text-red-500">Error loading subcategory data</p>
          <Button
            text="Close"
            onClick={() => setShowModal(false)}
            className="mt-4"
          />
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      title="Edit Request"
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      centered
      className="max-w-4xl"
    >
      <Formik
        initialValues={getInitialValues()}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, errors, touched, setFieldValue, isSubmitting }) => (
          <Form className="space-y-4">
            {/* Error Summary */}
            {submitAttempted && Object.keys(errors).length > 0 && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-md" role="alert">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Please fix the following errors:
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <ul className="list-disc list-inside space-y-1">
                        {Object.entries(errors).map(([field, error]) => (
                          <li key={field}>
                            {field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}: {error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Title */}
              <div className="md:col-span-2">
                <InputField
                  name="title"
                  label="Title"
                  required
                  placeholder="Enter request title"
                />
              </div>

              {/* Short Description */}
              <div className="md:col-span-2">
                <TextareaField
                  name="short_description"
                  label="Short Description"
                  placeholder="Brief description of the request"
                  rows={2}
                />
              </div>

              {/* Description */}
              <div className="md:col-span-2">
                <TextareaField
                  name="description"
                  label="Description"
                  placeholder="Detailed description of the request"
                  rows={4}
                />
              </div>

              {/* Quantity */}
              <div>
                <InputField
                  name="quantity"
                  label="Quantity"
                  type="number"
                  min="1"
                  placeholder="1"
                />
              </div>

              {/* Budget Min */}
              <div>
                <InputField
                  name="budget_min"
                  label="Minimum Budget"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>

              {/* Budget Max */}
              <div>
                <InputField
                  name="budget_max"
                  label="Maximum Budget"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>

              {/* Deadline */}
              <div>
                <InputField
                  name="deadline"
                  label="Deadline"
                  type="date"
                />
              </div>

              {/* Urgency */}
              <div>
                <label className="block mb-1 font-medium">Urgency</label>
                <select
                  name="urgency"
                  value={values.urgency}
                  onChange={(e) => setFieldValue('urgency', e.target.value)}
                  className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Low">Low</option>
                  <option value="Normal">Normal</option>
                  <option value="High">High</option>
                  <option value="Urgent">Urgent</option>
                </select>
              </div>

              {/* Request Type */}
              <div>
                <label className="block mb-1 font-medium">Request Type</label>
                <select
                  name="request_type"
                  value={values.request_type}
                  onChange={(e) => setFieldValue('request_type', e.target.value)}
                  className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="General">General</option>
                  <option value="Service">Service</option>
                  <option value="Product">Product</option>
                  <option value="Consultation">Consultation</option>
                </select>
              </div>

              {/* Location */}
              <div className="md:col-span-2">
                <InputField
                  name="location"
                  label="Location"
                  placeholder="Enter location"
                />
              </div>

              {/* Additional Info */}
              <div className="md:col-span-2">
                <TextareaField
                  name="additional_info"
                  label="Additional Information"
                  placeholder="Any additional information"
                  rows={3}
                />
              </div>
            </div>

            {/* Dynamic Form Fields */}
            {subcategoryData?.data?.form_fields && subcategoryData.data.form_fields.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Category Specific Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {subcategoryData.data.form_fields
                    .sort((a, b) => a.sort_order - b.sort_order)
                    .map((formField) => (
                      <div key={formField.id} className={formField.input_type === 'TEXTAREA' ? 'md:col-span-2' : ''}>
                        <DynamicFormField
                          formField={formField}
                          errors={errors}
                          touched={touched}
                          values={values}
                          setFieldValue={setFieldValue}
                        />
                      </div>
                    ))}
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                type="button"
                text="Cancel"
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                onClick={() => setShowModal(false)}
              />
              <Button
                type="submit"
                text={isSubmitting || isLoading ? 'Updating...' : 'Update Request'}
                isLoading={isSubmitting || isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400"
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditRequestModal;
