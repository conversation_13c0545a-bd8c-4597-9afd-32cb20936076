/**
 * Utility functions for authentication
 */

/**
 * Clears all authentication data from cookies and localStorage
 */
export const clearAuthData = () => {
  // Clear cookies by setting their expiration date to the past
  document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';
  document.cookie = 'refreshToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';
  
  // Clear localStorage items related to authentication
  localStorage.removeItem('user');
  localStorage.removeItem('lms_user');
  localStorage.removeItem('lms_user_role');
  
  // Clear any other auth-related localStorage items
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('auth') || key.includes('token') || key.includes('user'))) {
      keysToRemove.push(key);
    }
  }
  
  // Remove collected keys
  keysToRemove.forEach(key => localStorage.removeItem(key));
};

/**
 * Gets a cookie value by name
 * @param {string} name - The name of the cookie
 * @returns {string|null} The cookie value or null if not found
 */
export const getCookie = (name) => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
  return null;
};

/**
 * Sets a cookie with the given name, value and options
 * @param {string} name - The name of the cookie
 * @param {string} value - The value of the cookie
 * @param {number} daysToExpire - Number of days until the cookie expires
 */
export const setCookie = (name, value, daysToExpire = 1) => {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + daysToExpire);
  
  document.cookie = `${name}=${value}; path=/; expires=${expiryDate.toUTCString()}; SameSite=Strict`;
};
