import * as Yup from 'yup';

// Common validation rules that apply to both create and update
const commonValidationRules = {

    first_name: Yup.string()
    .required('The first name field is required.')
    .min(2, 'The first name must be between 2 and 50 characters.')
    .max(50, 'The first name must be between 2 and 50 characters.')
    .matches(
        /^[a-zA-ZÀ-ÿ\s']+$/,
        'First name should only contain letters and spaces'
    ),

    last_name: Yup.string()
    .required('The last name field is required.')
    .min(2, 'The last name must be between 2 and 50 characters.')
    .max(50, 'The last name must be between 2 and 50 characters.')
    .matches(
        /^[a-zA-ZÀ-ÿ\s']+$/,
        'Last name should only contain letters and spaces'
    ),

  email: Yup.string()
    .required('The email field is required.')
    .email('The email must be a valid email address.'),

  phone_number: Yup.string()
    .required('The phone number field is required.')
    .matches(/^[0-9+\-\s()]+$/, 'The phone number format is invalid.'),

  // Optional fields with validation
  father_name: Yup.string()
    .nullable()
    .max(50, "The father's name may not be greater than 50 characters."),

  mother_name: Yup.string()
    .nullable()
    .max(50, "The mother's name may not be greater than 50 characters."),

  date_of_birth: Yup.date()
    .nullable()
    .test('age', 'The age must be greater than 18.', function(value) {
      if (!value) return true; // Optional field
      const age = Math.floor((Date.now() - new Date(value).getTime()) / 1000 / 60 / 60 / 24 / 365.25);
      return age >= 18 && age <= 120;
    }),

  gender: Yup.string()
    .nullable()
    .oneOf(['male', 'female', 'other', ''], 'The selected gender is invalid.'),

  address: Yup.string()
    .nullable()
    .max(255, 'The address may not be greater than 255 characters.'),

  national_id_number: Yup.string()
    .nullable(),

  passport_number: Yup.string()
    .nullable(),

  // Role validation
  role: Yup.string()
    .required('The role field is required.')
    .oneOf(['Buyer', 'Seller', 'Supplier', 'Admin'], 'The selected role is invalid.'),

  city: Yup.string().nullable(),
  state: Yup.string().nullable(),
  zip_code: Yup.string().nullable(),
  country: Yup.string().nullable(),
};

// Validation schema for creating a new user
export const createUserValidationSchema = Yup.object().shape({
  ...commonValidationRules,
  password: Yup.string()
    .required('The password field is required.')
    .min(8, 'The password must be at least 8 characters.'),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref('password'), null], 'The password confirmation does not match.')
    .required('The password confirmation field is required.'),
});

// Validation schema for updating an existing user
export const updateUserValidationSchema = Yup.object().shape({
  ...commonValidationRules,
  password: Yup.string()
    .min(8, 'The password must be at least 8 characters.')
    .nullable(),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref('password'), null], 'The password confirmation does not match.')
    .when('password', {
      is: (val) => val && val.length > 0,
      then: Yup.string().required('Password confirmation is required when password is provided'),
      otherwise: Yup.string().nullable()
    }),
});

// Initial values based on validation rules
export const getInitialValues = () => ({
  // Required fields
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
  password: '',
  password_confirmation: '',
  role: 'Buyer', // Default role
  avatar: '', // For file upload

  // Optional fields
  father_name: '',
  mother_name: '',
  date_of_birth: '',
  gender: '',
  address: '',
  national_id_number: '',
  passport_number: '',
  city: '',
  state: '',
  zip_code: '',
  country: '',
});