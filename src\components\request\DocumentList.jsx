import React from "react";
import { getFileIcon } from "@/utils/fileIcons";

const DocumentList = ({ documentAttachments, handleDocumentClick }) => {
    if (!documentAttachments.length) return null;

    return (
        <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Additional Documents</h3>
            <div className="flex flex-wrap gap-3">
                {documentAttachments.map((doc) => {
                    const fileName = doc.file_path?.split('/').pop();
                    const fileExtension = fileName?.split('.').pop().toUpperCase();

                    return (
                        <div
                            key={doc.id}
                            className="flex flex-col items-center p-2 border rounded-md cursor-pointer hover:bg-gray-50 transition w-24"
                            onClick={() => handleDocumentClick(doc.file_path)}
                            title={`Click to open ${fileName}`}
                        >
                            {getFileIcon(doc.file_path)}
                            <span className="text-xs font-medium text-gray-500 mt-1">{fileExtension}</span>
                            <span className="text-xs text-gray-500 truncate w-full text-center">
                                {fileName.length > 12 ? fileName.substring(0, 10) + '...' : fileName}
                            </span>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default DocumentList;