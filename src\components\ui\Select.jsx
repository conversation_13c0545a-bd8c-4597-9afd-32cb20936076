import React, { Fragment } from "react";
import Icon from "@/components/ui/Icon";

const Select = ({
  label,
  placeholder = "Select Option",
  classLabel = "text-base text-gray-500",
  className = "",
  classGroup = "",
  register,
  name,
  readonly,
  value,
  error,
  icon,
  disabled,
  id,
  horizontal,
  validate,
  msgTooltip,
  description,
  onChange,
  options,
  defaultValue,
  required,
  size,
  ...rest
}) => {
  options = options || Array(3).fill("Select an option");

  // Function to clear the selection
  const handleClearSelection = () => {
    onChange({ target: { value: "" } }); // Clear selected value
  };

  return (
    <div
      className={`fromGroup  ${error ? "has-error" : ""}  ${
        horizontal ? "flex" : ""
      }  ${validate ? "is-valid" : ""}`}
    >
      {label && (
        <label
          htmlFor={id}
          className={`block capitalize ${classLabel}  ${
            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
          }`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className={`relative ${horizontal ? "flex-1" : ""}`}>
        {name && (
          <select
            onChange={onChange}
            // {...register(name)}
            {...rest}
            className={`${
              error ? " has-error" : " "
            } form-control py-2  appearance-none ${className}  `}
            placeholder={placeholder}
            readOnly={readonly}
            disabled={disabled}
            id={id}
            value={value}
            size={size}
            defaultValue={defaultValue}
          >
            <option value="" disabled className="hidden">
              {placeholder}
            </option>
            {options.map((option, i) => (
              <Fragment key={i}>
                {option.value && option.label ? (
                  <option key={i} value={option.value}>
                    {option.label}
                  </option>
                ) : (
                  <option key={i} value={option}>
                    {option}
                  </option>
                )}
              </Fragment>
            ))}
          </select>
        )}

        {/* Display the "clear" button when a value is selected */}
        {/* {value && ( */}
          <button
            type="button"
            onClick={handleClearSelection}
            className="absolute top-1/2 right-3 -translate-y-1/2 text-lg text-gray-500"
            aria-label="Clear selection"
          >
            <Icon icon="heroicons:mini-x-circle" />
          </button>
        {/* )} */}

        {/* icon */}
        <div className="flex text-xl absolute ltr:right-[14px] rtl:left-[14px] top-1/2 -translate-y-1/2 space-x-1 rtl:space-x-reverse z-10">
          <span className=" relative -right-2 inline-block text-slate-900 dark:text-slate-300 pointer-events-none">
            <Icon icon="heroicons:chevron-down" />
          </span>
          {validate && (
            <span className="text-success-500">
              <Icon icon="bi:check-lg" />
            </span>
          )}
        </div>
      </div>
      {/* error and success message */}
      {error && (
        <div
          className={` mt-2 ${
            msgTooltip
              ? " inline-block bg-danger-500 text-white text-xs px-2 py-1 rounded"
              : " text-danger-500 block text-sm"
          }`}
        >
          {error}
        </div>
      )}
      {validate && (
        <div
          className={` mt-2 ${
            msgTooltip
              ? " inline-block bg-success-500 text-white text-xs px-2 py-1 rounded"
              : " text-success-500 block text-sm"
          }`}
        >
          {validate}
        </div>
      )}
      {description && <span className="input-description">{description}</span>}
    </div>
  );
};

export default Select;
