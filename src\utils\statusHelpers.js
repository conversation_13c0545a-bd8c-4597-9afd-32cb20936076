/**
 * Format a date string to a localized date and time format
 * @param {string} dateString - The date string to format
 * @returns {string} Formatted date and time string
 */
export const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return 'Invalid date';
    }
};

/**
 * Get the appropriate color for a status
 * @param {string} status - The status to get color for
 * @returns {string} Color identifier for the status
 */
export const getStatusColor = (status) => {
    if (!status) return 'gray';

    switch (status.toLowerCase()) {
        case 'active':
        case 'approved':
        case 'completed':
            return 'success';

        case 'inactive':
        case 'in progress':
        case 'processing':
            return 'warning';

        case 'suspended':
        case 'rejected':
        case 'cancelled':
            return 'danger';

        case 'pending':
            return 'info';

        default:
            return 'gray';
    }
};

/**
 * Check if a file is an image based on its extension
 * @param {string} url - The file URL or path
 * @returns {boolean} True if the file is an image
 */
export const isImageFile = (url) => {
    if (!url) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
};