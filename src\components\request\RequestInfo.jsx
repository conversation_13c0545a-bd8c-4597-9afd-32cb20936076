import React from "react";
import { Link } from "react-router-dom";
import Button from "@/components/ui/Button";
import { getStatusColor } from "@/utils/statusHelpers";

const RequestInfo = ({ request, setShowApproveModal, setShowRejectModal }) => {
    return (
        <div className="space-y-3">
            <div className="flex justify-between items-start">
                <div>
                    <h2 className="text-2xl font-bold text-gray-800">{request.title || "Box Delivery Service"}</h2>
                    <div className="text-sm text-gray-500 font-mono mt-1">
                        {request.request_code || `REQ-${request.id}`}
                    </div>
                </div>
                <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-${getStatusColor(request.status)}-100 text-${getStatusColor(request.status)}-800`}>
                    {request.status}
                </span>
            </div>
            <div className="text-sm text-gray-500 space-x-4">
                <Link to={`/category/${request?.category?.id}`}>{request?.category?.title}</Link> -
                <span>{request?.sub_category?.title}</span>
            </div>

            <div className="text-xl font-semibold text-gray-700">
                ${request.budget_max} - ${request.budget_min}
            </div>
            <div className="text-sm text-gray-600">{request.quantity} Unit</div>
            <div className="text-sm text-gray-600">
                {new Date(request.deadline).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
            </div>

            <div>
                <p className="text-gray-800 font-semibold">Short Description:</p>
                <p className="text-gray-600">{request.short_description || "N/A"}</p>
            </div>

            <div>
                <p className="text-gray-800 font-semibold">Details Descriptions:</p>
                <p className="text-gray-600 whitespace-pre-line">{request.description || "N/A"}</p>
            </div>

            <div>
                <p className="text-gray-800 font-semibold">Additional Criteria:</p>
                <p className="text-gray-600">{request.additional_info || "N/A"}</p>
            </div>

            {/* Custom Fields */}
            {request.custom_fields && Object.keys(request.custom_fields).length > 0 && (
                <div>
                    <p className="text-gray-800 font-semibold">Category Specific Information:</p>
                    <div className="mt-3 bg-gray-50 rounded-lg p-4 space-y-3">
                        {Object.entries(request.custom_fields).map(([key, value]) => (
                            <div key={key} className="flex flex-col sm:flex-row">
                                <span className="text-sm font-medium text-gray-700 sm:w-1/3 mb-1 sm:mb-0">
                                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                                </span>
                                <span className="text-sm text-gray-900 sm:w-2/3 font-medium">
                                    {value || "N/A"}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3 mt-6">
                {request.status?.toLowerCase() !== 'approved' && (
                    <Button
                        onClick={() => setShowApproveModal(true)}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                        Approve
                    </Button>
                )}
                {request.status?.toLowerCase() !== 'rejected' && (
                <Button
                    onClick={() => setShowRejectModal(true)}
                    className="bg-red-500 hover:bg-red-600 text-white"
                >
                    Reject
                </Button>
                )}
                {/* <Button
                    className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                    Modify
                </Button> */}
            </div>
        </div>
    );
};

export default RequestInfo;