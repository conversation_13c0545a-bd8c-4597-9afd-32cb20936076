import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGetApiQuery } from '@/store/api/master/commonSlice';
import { skipToken } from '@reduxjs/toolkit/query';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import Icon from '@/components/ui/Icon';
import Loading from '@/components/Loading';
import Sellers from "./Sellers";
import AssignedSellers from "@/components/request/AssignedSellers";

const MergedRequestDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    data: requestData,
    isLoading: isRequestLoading,
    isError: isRequestError,
  } = useGetApiQuery(id ? `/admin/requests/${id}` : skipToken);

  const formatDate = (dateString) => {
    return dateString
      ? new Date(dateString).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })
      : 'N/A';
  };

  const formatDateTime = (dateString) => {
    return dateString
      ? new Date(dateString).toLocaleString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      : 'N/A';
  };

  const formatCurrency = (min, max) => {
    if (!min && !max) return 'Not specified';
    if (min && max) return `$${Number(min).toLocaleString()} - $${Number(max).toLocaleString()}`;
    if (min) return `$${Number(min).toLocaleString()}+`;
    return `Up to $${Number(max).toLocaleString()}`;
  };

  const getBadgeColor = (type, value) => {
    const map = {
      urgency: {
        urgent: 'bg-red-100 text-red-800',
        high: 'bg-orange-100 text-orange-800',
        normal: 'bg-blue-100 text-blue-800',
        low: 'bg-green-100 text-green-800',
      },
      status: {
        merged: 'bg-purple-100 text-purple-800',
        pending: 'bg-yellow-100 text-yellow-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800',
      },
    };
    return map[type]?.[value?.toLowerCase()] || 'bg-gray-100 text-gray-800';
  };

  if (isRequestLoading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-200px)]">
        <Loading />
      </div>
    );
  }

  if (isRequestError || !requestData?.data) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center px-4">
        <Icon icon="heroicons:exclamation-triangle" className="w-16 h-16 text-red-500 mb-4" />
        <h2 className="text-xl font-bold text-gray-800 mb-2">Request Not Found</h2>
        <p className="text-gray-600 mb-6 max-w-md">
          The merged request you're looking for doesn't exist or has been removed.
        </p>
        <Button variant="primary" onClick={() => navigate('/requests')}>
          <Icon icon="heroicons:arrow-left" className="mr-2 w-4 h-4" /> 
          Back to Requests
        </Button>
      </div>
    );
  }

  const request = requestData.data;

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <button 
              onClick={() => navigate('/requests')}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              <Icon icon="heroicons:arrow-left" className="w-5 h-5" />
            </button>
            <h1 className="text-2xl font-bold text-gray-800">Merged Request</h1>
          </div>
          <div className="flex items-center gap-2">
            <p className="text-sm text-gray-600 font-medium">
              {request.request_code}
            </p>
            <Badge className={getBadgeColor('status', request.status)}>
              {request.status}
            </Badge>
          </div>
        </div>
        <div className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => navigate('/requests')}
            className="border-gray-300 hover:border-gray-400"
          >
            Back to List
          </Button>
          {/* {request.is_merged && (
            <Button variant="primary">
              <Icon icon="heroicons:pencil-square" className="mr-2 w-4 h-4" /> 
              Edit Request
            </Button>
          )} */}
        </div>
      </div>

      {/* Main Card */}
      <Card className="border border-gray-200 shadow-sm">
        {/* Request Title Section */}
        <div className="border-b border-gray-200 pb-4 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">{request.title}</h2>
          <p className="text-gray-600">{request.short_description}</p>
        </div>

        {/* Grid Info Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Category</h3>
            <p className="text-gray-800 font-medium">
              {request.category.title} &gt; {request.sub_category.title}
            </p>
          </div>

          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Request Type</h3>
            <p className="text-gray-800 font-medium">{request.request_type}</p>
          </div>

          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Quantity</h3>
            <p className="text-gray-800 font-medium">{request.quantity}</p>
          </div>

          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Budget Range</h3>
            <p className="text-gray-800 font-medium">
              {formatCurrency(request.budget_min, request.budget_max)}
            </p>
          </div>

          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Deadline</h3>
            <p className="text-gray-800 font-medium">
              {formatDate(request.deadline)}
              {new Date(request.deadline) < new Date() && (
                <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">Expired</span>
              )}
            </p>
          </div>

          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Created</h3>
            <p className="text-gray-800 font-medium">{formatDateTime(request.created_at)}</p>
          </div>

          {request.location && (
            <div className="space-y-1">
              <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Location</h3>
              <p className="text-gray-800 font-medium">{request.location}</p>
            </div>
          )}

          <div className="space-y-1">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Last Updated</h3>
            <p className="text-gray-800 font-medium">{formatDateTime(request.updated_at)}</p>
          </div>

          {request.request_statuses?.[0] && (
            <div className="space-y-1">
              <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">Status Updated By</h3>
              <p className="text-gray-800 font-medium">
                {request.request_statuses[0].updated_by_user.first_name}{' '}
                {request.request_statuses[0].updated_by_user.last_name}
              </p>
              {request.request_statuses[0].reason && (
                <p className="text-sm text-gray-600 mt-1">
                  <span className="font-medium">Reason:</span> {request.request_statuses[0].reason}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Detailed Descriptions */}
        {request.description && (
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-700 mb-2 uppercase tracking-wider">Detailed Description</h3>
            <div className="bg-gray-50 text-gray-800 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
              {request.description}
            </div>
          </div>
        )}

        {request.additional_info && (
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-700 mb-2 uppercase tracking-wider">Additional Information</h3>
            <div className="bg-gray-50 text-gray-800 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
              {request.additional_info}
            </div>
          </div>
        )}
      </Card>

      {/* Merged Requests Section */}
      {request.merged_requests?.length > 0 && (
        <Card className="border border-gray-200 shadow-sm">
          <div className="border-b border-gray-200 pb-4 mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h2 className="text-lg font-semibold text-gray-800">
                Original Requests ({request.merged_requests.length})
              </h2>
              <Badge className="bg-purple-100 text-purple-800">
                Merged from {request.merged_requests.length} requests
              </Badge>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              These are the individual requests that were combined into this merged request
            </p>
          </div>

          <div className="space-y-4">
            {request.merged_requests.map(({ id, merged_item, merged_by_user }) => (
              <Card key={id} className="border border-gray-200 hover:border-gray-300 transition-colors">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4">
                  <div className="space-y-3 flex-1">
                    <div className="flex flex-wrap items-center gap-3">
                      <h2 className="text-xl font-semibold text-gray-800 mb-2">{merged_item.title}</h2>
                      {/* <h5 className="font-semibold text-gray-800">{merged_item.title}</h5> */}
                      <Badge className={getBadgeColor('urgency', merged_item.urgency)}>
                        {merged_item.urgency}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                      <div>
                        <span className="text-gray-500">Buyer:</span>{' '}
                        <span className="font-medium text-gray-700">
                          {merged_item.buyer.first_name} {merged_item.buyer.last_name}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">Budget:</span>{' '}
                        <span className="font-medium text-gray-700">
                          {formatCurrency(merged_item.budget_min, merged_item.budget_max)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">Created:</span>{' '}
                        <span className="font-medium text-gray-700">
                          {formatDate(merged_item.created_at)}
                        </span>
                      </div>
                    </div>

                    {merged_item.short_description && (
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {merged_item.short_description}
                      </p>
                    )}
                  </div>

                  <div className="flex-shrink-0">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate(`/request/${merged_item.id}`)}
                      className="border-gray-300 hover:border-gray-400"
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </Card>
      )}

      {/* Sellers Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Sellers requestId={request.id} assignedSellers={request.assigned_sellers} />
        <AssignedSellers assignedSellers={request.assigned_sellers} />
      </div>
    </div>
  );
};

export default MergedRequestDetails;