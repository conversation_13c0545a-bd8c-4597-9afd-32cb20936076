import React, { useState, useMemo, useEffect } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Link } from "react-router-dom";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import Loading from "@/components/Loading";
import Badge from "@/components/ui/Badge";

const UserPage = () => {
  const { role } = useParams();
  const location = useLocation();
  const navigate = useNavigate();


  // State for search term
  const [searchTerm, setSearchTerm] = useState('');
  const [apiParam, setApiParam] = useState('');

  // Construct query parameter for API
  const queryParam = useMemo(() => {
    const baseParam = `${apiParam?'&':'?'}role=${role.charAt(0).toUpperCase() + role.slice(1)}`;
    return apiParam ? `${apiParam}${baseParam}` : baseParam;
  }, [role, apiParam]);

  const {
    data: users,
    isLoading,
    isError,
    isFetching
  } = useGetApiQuery(`/admin/users${queryParam}`);

  const handleUserClick = (id) => {
    navigate(`/user/${id}`);
  };

  const tableData = users?.data?.map((item) => {
    return {
      id: item.id,
      name: (
        <button
          type="button"
          onClick={() => handleUserClick(item.id)}
          className="hover:text-primary-500 hover:underline"
        >
          {item.first_name} {item.last_name}
        </button>
      ),
      email: item.email,
      phone: item.phone_number,
      status: (
        <Badge
          className={
            item.status === "active"
              ? "bg-success-500 text-white"
              : "bg-danger-500 text-white"
          }
        >
          {item.status}
        </Badge>
      ),
      createdAt: new Date(item.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })
    };
  });

  // const changePage = (val) => {
  //   setApiParam(val ? `${newParams}&page=${val}` : newParams);

  // };
  const changePage = (val) => {
    setApiParam(val ? `?page=${val}` : '');
  };
  const handleSearch = (searchValue) => {
    setApiParam(searchValue);

  };

  const columns = [
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Email",
      field: "email",
    },
    {
      label: "Phone",
      field: "phone",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Joined",
      field: "createdAt",
    },
    {
      label: "Action",
      field: "",
    }
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/users/edit/${users.data[val].id}`);
      },
    },
    {
      name: "View",
      icon: "heroicons:eye",
      onClick: (val) => {
        navigate(`/users/${users.data[val].id}`);
      },
    }
  ];

  return (
    <div className="flex flex-col gap-5">

        <BasicTablePage
          title={`${role.charAt(0).toUpperCase() + role.slice(1)} Users`}
          CreateUrl="/users/create"
          columns={columns}
          actions={actions}
          data={tableData}
          changePage={changePage}
          currentPage={users?.meta?.currentPage}
          setFilter={setApiParam}
          totalPages={users?.meta?.pages}
          hideCreateButton={true}
        />
    </div>
  );
};

export default UserPage;