import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import TextArea from "@/components/ui/Textarea";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const BulkApproveModal = ({ showModal, setShowModal, selectedRequests, onSuccess }) => {
    const [postApi, { isLoading }] = usePostApiMutation();
    const [notes, setNotes] = useState("");

    const onSubmit = async () => {
        try {
            const requestBody = {
                request_ids: selectedRequests,
                action: 'approve'
            };

            // Add note only if provided
            if (notes.trim()) {
                requestBody.note = notes.trim();
            }

            const response = await postApi({
                end_point: '/admin/requests/bulk-action',
                body: requestBody
            });

            if (!response.error) {
                setShowModal(false);
                setNotes("");
                if (onSuccess) onSuccess();
            } else {
                toast.error(response.error?.data?.message || "Failed to approve requests");
            }
        } catch (error) {
            toast.error("An error occurred while approving requests");
            console.error(error);
        }
    };

    return (
        <Modal
            title="Bulk Approve Requests"
            themeClass="bg-success-500 dark:bg-success-700"
            centered={true}
            className="max-w-md"
            activeModal={showModal}
            onClose={() => setShowModal(false)}
        >
            <div className="text-base text-gray-700 dark:text-slate-300">
                <div className="flex items-center mb-6 bg-success-50 p-4 rounded-lg border border-success-100">
                    <div className="mr-4">
                        <div className="h-12 w-12 rounded-full bg-success-100 flex items-center justify-center">
                            <Icon icon="heroicons:check" className="h-6 w-6 text-success-600" />
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Confirm Bulk Approval</h3>
                        <p className="text-sm text-gray-600">
                            Are you sure you want to approve {selectedRequests.length} selected request(s)?
                        </p>
                    </div>
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Approval Notes (Optional)
                    </label>
                    <TextArea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="Add notes for this bulk approval..."
                        className="w-full"
                        rows={3}
                    />
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                    onClick={() => setShowModal(false)}
                    icon="heroicons:x-mark"
                >
                    Cancel
                </Button>
                <Button
                    variant="success"
                    onClick={onSubmit}
                    isLoading={isLoading}
                    icon="heroicons:check"
                >
                    Approve {selectedRequests.length} Request(s)
                </Button>
            </div>
        </Modal>
    );
};

export default BulkApproveModal;
