import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import SectionHeader from "@/components/ui/SectionHeader";
import InfoRow from "@/components/ui/InfoRow";
import AttachmentCard from "@/components/ui/AttachmentCard";
import StatusHistoryCard from "@/components/ui/StatusHistoryCard";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";

const RequestDetailsPage = () => {
    const { id } = useParams();
    const [showApproveModal, setShowApproveModal] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);

    const { data: responseData, isLoading, isError } = useGetApiQuery(`admin/requests/${id}`);

    if (isLoading) return <Loading />;
    if (isError) return <div className="text-red-500">Error fetching data</div>;

    const request = responseData.data;

    const renderStatusBadge = (status) => {
        const statusColors = {
            Pending: "bg-yellow-100 text-yellow-800",
            Approved: "bg-green-100 text-green-800",
            Rejected: "bg-red-100 text-red-800"
        };
        return (
            <Badge className={`${statusColors[status] || 'bg-gray-100 text-gray-800'} px-3 py-1 rounded-full text-sm font-medium`}>
                {status}
            </Badge>
        );
    };

    const renderUrgencyBadge = (urgency) => {
        const urgencyColors = {
            High: "bg-red-100 text-red-800",
            Medium: "bg-yellow-100 text-yellow-800",
            Low: "bg-green-100 text-green-800"
        };
        return (
            <Badge className={`${urgencyColors[urgency] || 'bg-gray-100 text-gray-800'} px-3 py-1 rounded-full text-sm font-medium`}>
                {urgency}
            </Badge>
        );
    };

    return (
        <div className="px-4 py-6">
            {/* Page Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800">Request Details</h1>
                    {/* <p className="text-gray-600">ID: {id}</p> */}
                </div>
                <div className="flex flex-wrap gap-2">
                    <Link 
                        to="/requests" 
                        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                        Back to Requests
                    </Link>
                    {request.status !== 'Approved' && (
                        <Button 
                            onClick={() => setShowApproveModal(true)}
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            Approve Request
                        </Button>
                    )}
                    {request.status !== 'Rejected' && (
                        <Button 
                            onClick={() => setShowRejectModal(true)}
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                            Reject Request
                        </Button>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="space-y-6">
                {/* Request Overview Card */}
                <Card>
                    <SectionHeader title="Request Overview" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-4">
                            <InfoRow label="Title" value={request.title} />
                            <InfoRow label="Description" value={request.description || "N/A"} />
                            <InfoRow label="Quantity" value={request.quantity} />
                            <InfoRow label="Budget Range" value={`$${request.budget_min} - $${request.budget_max}`} />
                            <InfoRow label="Deadline" value={new Date(request.deadline).toLocaleString()} />
                        </div>
                        <div className="space-y-4">
                            <InfoRow label="Status" value={renderStatusBadge(request.status)} />
                            <InfoRow label="Urgency" value={renderUrgencyBadge(request.urgency)} />
                            <InfoRow label="Request Type" value={request.request_type} />
                            <InfoRow label="Location" value={request.location || "N/A"} />
                            <InfoRow label="Additional Info" value={request.additional_info || "N/A"} />
                        </div>
                    </div>
                </Card>

                {/* Buyer & Category Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                        <SectionHeader title="Buyer Information" />
                        <div className="space-y-4">
                            <InfoRow label="Name" value={`${request.buyer.first_name} ${request.buyer.last_name}`} />
                            <InfoRow label="Email" value={request.buyer.email} />
                            <InfoRow label="Phone" value={request.buyer.phone_number} />
                            <InfoRow label="Account Status" value={renderStatusBadge(request.buyer.status)} />
                        </div>
                    </Card>

                    <Card>
                        <SectionHeader title="Category Information" />
                        <div className="space-y-4">
                            <InfoRow label="Category" value={request.category.title} />
                            <InfoRow label="Subcategory" value={request.sub_category.title} />
                        </div>
                    </Card>
                </div>

                {/* Attachments Section */}
                <Card>
                    <SectionHeader 
                        title="Attachments" 
                        subtitle={`${request.request_attachments.length} file(s) attached`}
                    />
                    {request.request_attachments.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {request.request_attachments.map(attachment => (
                                <AttachmentCard
                                    key={attachment.id}
                                    name={attachment.description || "Unnamed file"}
                                    type={attachment.file_type}
                                    size={attachment.file_size}
                                    url={`${import.meta.env.VITE_ASSET_HOST_URL}${attachment.file_path}`}
                                />
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">No attachments available</p>
                    )}
                </Card>

                {/* Status History Section */}
                <Card>
                    <SectionHeader 
                        title="Status History" 
                        subtitle={`${request.request_statuses.length} status change(s)`}
                    />
                    {request.request_statuses.length > 0 ? (
                        <div className="space-y-4">
                            {request.request_statuses.map(status => (
                                <StatusHistoryCard
                                    key={status.id}
                                    status={status.status}
                                    previousStatus={status.previous_status}
                                    reason={status.reason}
                                    updatedBy={`${status.updated_by_user.first_name} ${status.updated_by_user.last_name}`}
                                    updatedAt={new Date(status.created_at).toLocaleString()}
                                />
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">No status history available</p>
                    )}
                </Card>
            </div>

            {/* Modals */}
            {showApproveModal && 
            <ApproveModal 
                isOpen={showApproveModal}
                onClose={() => setShowApproveModal(false)}
                request={request}
            /> }
            {showRejectModal && 
            <RejectModal 
                isOpen={showRejectModal}
                onClose={() => setShowRejectModal(false)}
                request={request}
            />
            }
        </div>
    );
};

export default RequestDetailsPage;