import React, { forwardRef } from "react";
import { useField, useFormikContext } from "formik";

const TextareaField = forwardRef(({
  classLabel = "form-label",
  horizontal,
  label,
  required,
  error,
  rows = 3,
  ...props
}, ref) => {
  const [field, meta] = useField(props);
  const { submitCount } = useFormikContext();
  
  // Show error if field is touched, or form was submitted
  const isError = (meta.touched || submitCount > 0) && meta.error;

  // Ensure field value is never null to avoid React warnings
  if (field.value === null) {
    field.value = '';
  }

  return (
    <div className={`fromGroup ${isError ? "has-error" : ""} ${horizontal ? "flex" : ""}`}>
      {label && (
        <label
          htmlFor={props.id || props.name}
          className={`block capitalize ${classLabel} ${
            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
          }`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className={horizontal ? "flex-1" : ""}>
        <textarea
          {...field}
          {...props}
          rows={rows}
          className={`form-control py-2 resize-none ${
            isError ? "border-red-500" : ""
          }`}
          ref={ref}
        />
        {isError && (
          <span className="text-red-500 text-xs block mt-1">
            {meta.error}
          </span>
        )}
      </div>
    </div>
  );
});

export default TextareaField;
