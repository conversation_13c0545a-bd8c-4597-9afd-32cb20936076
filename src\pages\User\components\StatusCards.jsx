import React from 'react';
import Icon from "@/components/ui/Icon";

const StatusCards = ({ user }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
      <div className="bg-white rounded-lg shadow-sm p-3 md:p-4 border-l-4 border-primary-500">
        <div className="flex justify-between items-start">
          <div className="min-w-0 flex-1">
            <p className="text-xs md:text-sm font-medium text-gray-500">Account Status</p>
            <p className="mt-1 text-lg md:text-xl font-semibold text-gray-900 capitalize truncate">{user.status || 'Unknown'}</p>
          </div>
          <div className={`rounded-full p-1.5 md:p-2 flex-shrink-0 ml-2 ${
            user.status?.toLowerCase() === 'active' ? 'bg-green-100 text-green-600' :
            user.status?.toLowerCase() === 'inactive' ? 'bg-yellow-100 text-yellow-600' :
            'bg-red-100 text-red-600'
          }`}>
            <Icon
              icon={
                user.status?.toLowerCase() === 'active' ? "heroicons:check-circle" :
                user.status?.toLowerCase() === 'inactive' ? "heroicons:clock" :
                "heroicons:x-circle"
              }
              className="h-4 w-4 md:h-5 md:w-5"
            />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-3 md:p-4 border-l-4 border-blue-500">
        <div className="flex justify-between items-start">
          <div className="min-w-0 flex-1">
            <p className="text-xs md:text-sm font-medium text-gray-500">Email Verification</p>
            <p className="mt-1 text-lg md:text-xl font-semibold text-gray-900 truncate">
              {user.is_email_verified ? 'Verified' : 'Not Verified'}
            </p>
          </div>
          <div className={`rounded-full p-1.5 md:p-2 flex-shrink-0 ml-2 ${
            user.is_email_verified ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'
          }`}>
            <Icon
              icon={user.is_email_verified ? "heroicons:envelope-check" : "heroicons:envelope"}
              className="h-4 w-4 md:h-5 md:w-5"
            />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-3 md:p-4 border-l-4 border-purple-500 sm:col-span-2 lg:col-span-1">
        <div className="flex justify-between items-start">
          <div className="min-w-0 flex-1">
            <p className="text-xs md:text-sm font-medium text-gray-500">Account Approval</p>
            <p className="mt-1 text-lg md:text-xl font-semibold text-gray-900 truncate">
              {user.is_approved ? 'Approved' : 'Pending'}
            </p>
          </div>
          <div className={`rounded-full p-1.5 md:p-2 flex-shrink-0 ml-2 ${
            user.is_approved ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'
          }`}>
            <Icon
              icon={user.is_approved ? "heroicons:check-badge" : "heroicons:clock"}
              className="h-4 w-4 md:h-5 md:w-5"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusCards;
