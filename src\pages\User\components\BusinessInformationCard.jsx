import React, { useState } from 'react';
import Card from '@/components/ui/Card';
import SectionHeader from '@/components/ui/SectionHeader';
import Badge from '@/components/ui/Badge';
import { FiMapPin, FiPhone, FiMail, FiGlobe, FiCalendar, FiUsers, FiDollarSign, FiClock, FiAward,  FiEye, FiEyeOff } from 'react-icons/fi';

const BusinessInformationCard = ({ user }) => {
  const [expandedBusiness, setExpandedBusiness] = useState(null);

  if (!user.business_informations || user.business_informations.length === 0) {
    return null;
  }

  const toggleExpanded = (businessId) => {
    setExpandedBusiness(expandedBusiness === businessId ? null : businessId);
  };

  const getVerificationBadge = (isVerified, verificationStatus) => {
    if (isVerified) {
      return <Badge variant="success" size="xs">Verified</Badge>;
    }
    
    switch (verificationStatus) {
      case 'pending':
        return <Badge variant="warning" size="xs">Pending</Badge>;
      case 'rejected':
        return <Badge variant="danger" size="xs">Rejected</Badge>;
      default:
        return <Badge variant="default" size="xs">Not Verified</Badge>;
    }
  };

  const getStatusBadge = (isActive) => {
    return isActive 
      ? <Badge variant="success" size="xs">Active</Badge>
      : <Badge variant="danger" size="xs">Inactive</Badge>;
  };

  const formatOperatingHours = (operatingHours) => {
    if (!operatingHours) return 'Not specified';
    
    const days = Object.entries(operatingHours);
    if (days.length === 0) return 'Not specified';
    
    return days.map(([day, hours]) => (
      <div key={day} className="flex justify-between text-xs">
        <span className="capitalize font-medium">{day}:</span>
        <span>{hours}</span>
      </div>
    ));
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return null;
    if (imagePath.startsWith('http')) return imagePath;
    return `${import.meta.env.VITE_ASSET_HOST_URL}${imagePath}`;
  };

  return (
    <Card>
      <SectionHeader 
        title="Business Information" 
        description="Business details and company information"
        badge={<Badge variant="primary" size="xs">{user.business_informations.length}</Badge>}
      />
      
      <div className="space-y-6 mt-4">
        {user.business_informations.map((business, index) => {
          const isExpanded = expandedBusiness === business.id;
          
          return (
            <div key={business.id} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* Business Header */}
              <div className="p-4 bg-gray-50 border-b border-gray-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {business.logo_url && (
                        <img
                          src={getImageUrl(business.logo_url)}
                          alt={business.business_name}
                          className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      )}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{business.business_name}</h3>
                        <p className="text-sm text-gray-600">{business.business_type}</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{business.short_description}</p>
                    <div className="flex flex-wrap gap-2">
                      {getVerificationBadge(business.is_verified, business.verification_status)}
                      {getStatusBadge(business.is_active)}
                      <Badge variant="info" size="xs">{business.business_category}</Badge>
                    </div>
                  </div>
                  <button
                    onClick={() => toggleExpanded(business.id)}
                    className="ml-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    title={isExpanded ? "Show less" : "Show more"}
                  >
                    {isExpanded ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* Business Details */}
              <div className="p-4">
                {/* Basic Info - Always visible */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <FiMapPin className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-xs font-medium text-gray-700">Location</p>
                      <p className="text-sm">{business.city}, {business.country}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <FiPhone className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-xs font-medium text-gray-700">Phone</p>
                      <p className="text-sm">{business.phone_number}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <FiMail className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-xs font-medium text-gray-700">Email</p>
                      <p className="text-sm">{business.email}</p>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                {isExpanded && (
                  <div className="space-y-6 pt-4 border-t border-gray-200">
                    {/* Banner Image */}
                    {business.banner_url && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Banner Image</h4>
                        <img
                          src={getImageUrl(business.banner_url)}
                          alt={`${business.business_name} banner`}
                          className="w-full h-32 object-cover rounded-lg border border-gray-200"
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      </div>
                    )}

                    {/* Long Description */}
                    {business.long_description && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
                        <p className="text-sm text-gray-600">{business.long_description}</p>
                      </div>
                    )}

                    {/* Contact & Location Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-3">Contact Information</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <FiMapPin className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">{business.address}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FiGlobe className="w-4 h-4 text-gray-500" />
                            <a 
                              href={business.website_url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 hover:underline"
                            >
                              {business.website_url}
                            </a>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Postal Code:</span> {business.postal_code}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-3">Business Details</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <FiCalendar className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">Established: {business.established_year}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FiUsers className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">Employees: {business.employee_count}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FiDollarSign className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">Revenue: {business.annual_revenue}</span>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">License:</span> {business.business_license}
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Tax ID:</span> {business.tax_id}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Operating Hours */}
                    {business.operating_hours && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                          <FiClock className="w-4 h-4" />
                          Operating Hours
                        </h4>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {formatOperatingHours(business.operating_hours)}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Services Offered */}
                    {business.services_offered && business.services_offered.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                          {/* <FiService className="w-4 h-4" /> */}
                          Services Offered
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {business.services_offered.map((service, idx) => (
                            <Badge key={idx} variant="default" size="sm">{service}</Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Certifications */}
                    {business.certifications && business.certifications.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                          <FiAward className="w-4 h-4" />
                          Certifications
                        </h4>
                        <div className="space-y-2">
                          {business.certifications.map((cert, idx) => (
                            <div key={idx} className="bg-gray-50 p-2 rounded text-sm">{cert}</div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Timestamps */}
                    <div className="pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
                        <div>
                          <span className="font-medium">Created:</span> {new Date(business.created_at).toLocaleDateString()}
                        </div>
                        <div>
                          <span className="font-medium">Updated:</span> {new Date(business.updated_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

export default BusinessInformationCard;
