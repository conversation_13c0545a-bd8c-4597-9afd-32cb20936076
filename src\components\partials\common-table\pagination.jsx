import React, { useState, useEffect } from "react";
import Icon from "@/components/ui/Icon";

const Pagination = ({
  totalPages,
  currentPage,
  handlePageChange,
  text,
  className = "",
}) => {
  const [pages, setPages] = useState([]);

  useEffect(() => {
    let startFrom = Math.max(1, currentPage - 2);
    let endTo = Math.min(totalPages, currentPage + 2);

    // Ensure pagination always displays 5 pages if possible
    if (endTo - startFrom < 4) {
      if (startFrom === 1) {
        endTo = Math.min(5, totalPages);
      } else if (endTo === totalPages) {
        startFrom = Math.max(totalPages - 4, 1);
      }
    }

    const pageNumbers = [];
    for (let i = startFrom; i <= endTo; i++) {
      pageNumbers.push(i);
    }
    setPages(pageNumbers);
  }, [totalPages, currentPage]);

  return (
    <div className={`${className} mt-6 flex justify-center`}>
      <ul className="flex items-center gap-2 bg-white dark:bg-gray-800 p-2 rounded-lg shadow-md">
        {/* Previous Button */}
        <li>
          <button
            className="px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            {text ? "Previous" : <Icon icon="heroicons-outline:chevron-left" />}
          </button>
        </li>

        {/* Show Ellipsis for First Page */}
        {pages[0] > 1 && (
          <>
            <li>
              <button
                className="px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition"
                onClick={() => handlePageChange(1)}
              >
                1
              </button>
            </li>
            {pages[0] > 2 && <li className="text-gray-500 dark:text-gray-400">...</li>}
          </>
        )}

        {/* Page Numbers */}
        {pages.map((page) => (
          <li key={page}>
            <button
              className={`px-3 py-2 rounded-md transition ${
                page === currentPage
                  ? "bg-blue-500 text-white dark:bg-blue-400"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
              }`}
              onClick={() => handlePageChange(page)}
              disabled={page === currentPage}
              aria-current={page === currentPage ? "page" : undefined}
            >
              {page}
            </button>
          </li>
        ))}

        {/* Show Ellipsis for Last Page */}
        {pages[pages.length - 1] < totalPages && (
          <>
            {pages[pages.length - 1] < totalPages - 1 && (
              <li className="text-gray-500 dark:text-gray-400">...</li>
            )}
            <li>
              <button
                className="px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition"
                onClick={() => handlePageChange(totalPages)}
              >
                {totalPages}
              </button>
            </li>
          </>
        )}

        {/* Next Button */}
        <li>
          <button
            className="px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            aria-label="Next page"
          >
            {text ? "Next" : <Icon icon="heroicons-outline:chevron-right" />}
          </button>
        </li>
      </ul>
    </div>
  );
};

export default Pagination;
