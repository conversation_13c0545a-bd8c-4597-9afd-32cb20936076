import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import { toast } from 'react-toastify';

const DeleteModal = ({ showDeleteModal, setShowDeleteModal, data, onSuccess }) => {
  const [deletePost, { isLoading }] = useDeleteApiMutation();

  const handleDelete = async () => {
    try {
      const response = await deletePost({
        end_point: `/admin/blog/posts/${data.id}`
      });

      if (response.error) {
        console.error('Error deleting blog post:', response.error);
        toast.error(response.error.data?.message || 'Failed to delete blog post');
      } else {
        toast.success('Blog post deleted successfully!');
        setShowDeleteModal(false);
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error deleting blog post:', error);
      toast.error('An error occurred while deleting the blog post');
    }
  };

  return (
    <Modal
      title="Delete Blog Post"
      labelclassName="btn-outline-dark"
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
    >
      <div className="text-base text-slate-600 dark:text-slate-300">
        <div className="flex items-center space-x-3 mb-4">
          <div className="flex-none">
            <Icon 
              icon="heroicons:exclamation-triangle" 
              className="text-danger-500 text-2xl" 
            />
          </div>
          <div className="flex-1">
            <h4 className="text-lg font-medium text-slate-800 dark:text-slate-100 mb-2">
              Confirm Deletion
            </h4>
            <p className="text-slate-600 dark:text-slate-300">
              Are you sure you want to delete the blog post "{data?.title}"? 
              This action cannot be undone.
            </p>
          </div>
        </div>

        {data && (
          <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4 mb-4">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="font-medium">Title:</span>
                <span className="text-slate-600 dark:text-slate-300">{data.title}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Status:</span>
                <span className="text-slate-600 dark:text-slate-300">{data.status}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Created:</span>
                <span className="text-slate-600 dark:text-slate-300">
                  {new Date(data.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="flex space-x-4 justify-end pt-4 border-t border-slate-200 dark:border-slate-600">
          <Button
            text="Cancel"
            className="btn-outline-secondary"
            onClick={() => setShowDeleteModal(false)}
            disabled={isLoading}
          />
          <Button
            text={isLoading ? "Deleting..." : "Delete"}
            className="btn-danger"
            onClick={handleDelete}
            disabled={isLoading}
            isLoading={isLoading}
            icon={isLoading ? null : "heroicons:trash"}
          />
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal;
