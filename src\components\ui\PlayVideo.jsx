import React, { useState, useEffect } from 'react';
import ReactPlayer from 'react-player';

const PlayVideo = ({ url, setDuration }) => {
  // const [duration, setDuration] = useState(0);
  const [videoTitle, setVideoTitle] = useState('');

  const handleDuration = (duration) => {
    setDuration(duration);
  };

  useEffect(() => {
    const fetchVideoTitle = async () => {
      try {
        const response = await fetch(`https://noembed.com/embed?url=${url}`);
        const data = await response.json();
        if (data && data.title) {
          setVideoTitle(data.title);
        }
      } catch (error) {
        console.error("Error fetching video title:", error);
      }
    };

    if (url) {
      fetchVideoTitle();
    }
  }, [url]);

  return (
    <div className='w-full h-full'>
      {ReactPlayer.canPlay(url) ? (
        <>
          {/* <h3>{videoTitle}</h3> */}
          <ReactPlayer
            url={url}
            controls
            width="100%"
            height="100%"
            onDuration={handleDuration}
          />
        </>
      ) : (
        <p>Sorry, this video format is not supported.</p>
      )}
    </div>
  );
};

export default PlayVideo;

