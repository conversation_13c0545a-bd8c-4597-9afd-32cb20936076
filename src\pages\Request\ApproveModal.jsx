import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import TextArea from "@/components/ui/Textarea";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const Approve = ({ showApproveModal, setShowApproveModal, data }) => {
    const [updateApi, { isLoading }] = useUpdateApiMutation();
    const [notes, setNotes] = useState("");

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    const onSubmit = async () => {
        try {
            const response = await updateApi({
                end_point: '/admin/requests/' + data?.id,
                body: {
                    status: 'Approved',
                    reason: notes.trim() || 'Approved by admin'
                }
            });

            if (response.error) {
                toast.error(response.error.data?.message || 'Failed to approve request');
            } else {
                setShowApproveModal(false);
            }
        } catch (error) {
            console.error('Error approving request:', error);
            toast.error('An error occurred while approving the request');
        }
    };

    return (
        <Modal
            title="Approve Request"
            themeClass="bg-success-500 dark:bg-success-700"
            centered={true}
            className="max-w-md"
            activeModal={showApproveModal}
            onClose={() => setShowApproveModal(false)}
        >
            <div className="text-base text-gray-700 dark:text-slate-300">
                <div className="flex items-center mb-6 bg-success-50 p-4 rounded-lg border border-success-100">
                    <div className="mr-4">
                        <div className="h-12 w-12 rounded-full bg-success-100 flex items-center justify-center">
                            <Icon icon="heroicons:check" className="h-6 w-6 text-success-600" />
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Confirm Approval</h3>
                        <p className="text-sm text-gray-600">Are you sure you want to approve this request?</p>
                    </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-100">
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Request Details</h4>
                    <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Title:</span>
                            <span className="text-gray-800">{data?.title}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Category:</span>
                            <span className="text-gray-800">{data?.category?.title || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">Budget:</span>
                            <span className="text-gray-800 font-semibold">
                                {formatCurrency(data?.budget_min || 0)} - {formatCurrency(data?.budget_max || 0)}
                            </span>
                        </div>
                        <div className="flex justify-between text-sm">
                            <span className="font-medium text-gray-600">User:</span>
                            <span className="text-gray-800">
                                {data?.user?.first_name} {data?.user?.last_name}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Approval Notes (Optional)
                    </label>
                    <TextArea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="Add any notes about this approval..."
                        className="w-full"
                        rows={3}
                    />
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                    onClick={() => setShowApproveModal(false)}
                    icon="heroicons:x-mark"
                >
                    Cancel
                </Button>
                <Button
                    variant="success"
                    onClick={onSubmit}
                    isLoading={isLoading}
                    icon="heroicons:check"
                >
                    Approve Request
                </Button>
            </div>
        </Modal>
    );
};

export default Approve;
