import React from 'react';
import Button from "@/components/ui/Button";

const AccountActionsCard = ({ user }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900">Account Actions</h3>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {!user.is_email_verified && (
            <Button
              variant="outline"
              className="py-2 text-sm"
              onClick={() => {
                alert('Verification email would be sent here');
              }}
              icon="heroicons-outline:mail"
              iconPosition="left"
            >
              Send Verification Email
            </Button>
          )}
          
          {!user.is_approved && (
            <Button
              variant="success"
              className="py-2 text-sm"
              onClick={() => {
                alert('User approval would happen here');
              }}
              icon="heroicons-outline:check"
              iconPosition="left"
            >
              Approve User
            </Button>
          )}
          
          {user.status?.toLowerCase() === 'active' ? (
            <Button
              variant="warning"
              className="py-2 text-sm"
              onClick={() => {
                alert('User suspension would happen here');
              }}
              icon="heroicons-outline:pause"
              iconPosition="left"
            >
              Suspend User
            </Button>
          ) : (
            <Button
              variant="success"
              className="py-2 text-sm"
              onClick={() => {
                alert('User activation would happen here');
              }}
              icon="heroicons-outline:play"
              iconPosition="left"
            >
              Activate User
            </Button>
          )}
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-red-600">Danger Zone</h4>
              <p className="mt-1 text-xs text-gray-500">Permanently delete this user and all their data</p>
            </div>
            <Button
              variant="danger"
              className="py-2 text-sm"
              onClick={() => {
                if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                  alert('User deletion would happen here');
                }
              }}
              icon="heroicons-outline:trash"
              iconPosition="left"
            >
              Delete User
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountActionsCard;
