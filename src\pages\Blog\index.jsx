import React, { useState, useEffect } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import DeleteModal from "./DeleteModal";

const BlogPostsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [apiParam, setApiParam] = useState("");
  const [selectedPosts, setSelectedPosts] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  // Get status from URL path
  const pathSegments = location.pathname.split('/');
  const statusParam = pathSegments[pathSegments.length - 1];
  const validStatuses = ['published', 'draft', 'featured', 'scheduled', 'archived'];
  const currentStatus = validStatuses.includes(statusParam) ? statusParam : null;

  // Build API endpoint with filters
  const buildApiEndpoint = () => {
    let endpoint = '/admin/blog/posts';
    const params = new URLSearchParams();
    
    if (currentStatus) {
      if (currentStatus === 'featured') {
        params.append('is_featured', 'true');
      } else {
        params.append('status', currentStatus);
      }
    }
    
    if (apiParam) {
      const urlParams = new URLSearchParams(apiParam.startsWith('&') ? apiParam.slice(1) : apiParam);
      urlParams.forEach((value, key) => {
        params.append(key, value);
      });
    }
    
    const queryString = params.toString();
    return queryString ? `${endpoint}?${queryString}` : endpoint;
  };

  const {
    data: posts,
    isLoading,
    isError,
    isFetching,
    refetch
  } = useGetApiQuery(buildApiEndpoint());

  // Debug logging
  console.log('Posts data:', posts);

  console.log(posts);

  // Handle post selection
  const handleSelectPost = (postId) => {
    setSelectedPosts(prev => 
      prev.includes(postId) 
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPosts.length === posts?.data?.data?.length) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(posts?.data?.data?.map(post => post.id) || []);
    }
  };

  const isAllSelected = selectedPosts.length === posts?.data?.data?.length && posts?.data?.data?.length > 0;
  const isIndeterminate = selectedPosts.length > 0 && selectedPosts.length < posts?.data?.data?.length;

  // Handle post click
  const handlePostClick = (id) => {
    navigate(`/blog/post/${id}`);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'published': return 'bg-green-500 text-white';
      case 'draft': return 'bg-gray-500 text-white';
      case 'scheduled': return 'bg-blue-500 text-white';
      case 'archived': return 'bg-red-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  // Table data
  const tableData = posts?.data?.data?.map((item) => {
    return {
      id: item.id,
      select: (
        <input
          type="checkbox"
          checked={selectedPosts.includes(item.id)}
          onChange={() => handleSelectPost(item.id)}
          className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
        />
      ),
      title: (
        <button
          type="button"
          onClick={() => handlePostClick(item.id)}
          className="hover:text-primary-500 hover:underline text-left"
        >
          <div className="font-medium">{item.title}</div>
          {item.excerpt && (
            <div className="text-sm text-gray-500 mt-1 line-clamp-2">
              {item.excerpt}
            </div>
          )}
        </button>
      ),
      author: item.author ? `${item.author.first_name} ${item.author.last_name}` : 'N/A',
      category: item.category?.title || 'Uncategorized',
      status: (
        <Badge className={getStatusColor(item.status)}>
          {item.status}
        </Badge>
      ),
      featured: (
        <Badge className={item.is_featured ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-gray-700'}>
          {item.is_featured ? 'Featured' : 'Regular'}
        </Badge>
      ),
      reading_time: item.reading_time ? `${item.reading_time} min` : 'N/A',
      created_at: formatDate(item.created_at),
      updated_at: formatDate(item.updated_at)
    };
  }) || [];

  const changePage = (val) => {
    const searchParams = new URLSearchParams(location.search);
    const searchQuery = searchParams.get('search') || '';

    if (val) {
      setApiParam(searchQuery ? `&search=${searchQuery}&page=${val}` : `&page=${val}`);
    } else {
      setApiParam(searchQuery ? `&search=${searchQuery}` : '');
    }
  };

  const columns = [
    {
      label: (
        <input
          type="checkbox"
          checked={isAllSelected}
          ref={(input) => {
            if (input) input.indeterminate = isIndeterminate;
          }}
          onChange={handleSelectAll}
          className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
        />
      ),
      field: "select",
    },
    {
      label: "Title",
      field: "title",
    },
    {
      label: "Author",
      field: "author",
    },
    {
      label: "Category",
      field: "category",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Featured",
      field: "featured",
    },
    {
      label: "Reading Time",
      field: "reading_time",
    },
    {
      label: "Created",
      field: "created_at",
    },
    {
      label: "Updated",
      field: "updated_at",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        const postId = posts.data.data[val].id;
        navigate(`/blog/post/edit/${postId}`);
      },
      className: "text-primary-500 hover:text-primary-600"
    },
    {
      name: "Delete",
      icon: "heroicons:trash",
      onClick: (val) => {
        setDeleteData(posts.data.data[val]);
        setShowDeleteModal(true);
      },
      className: "text-danger-500 hover:text-danger-600"
    },
  ];

  return (
    <div className="flex flex-col gap-5">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">
          {currentStatus ? `${currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)} Posts` : 'Blog Posts'}
        </h2>
        <Link to="/blog/post/create" className="btn btn-primary">
          <Icon icon="heroicons:plus" className="mr-2" />
          Create Post
        </Link>
      </div>

      {(isLoading || (isFetching)) ? (
        <Loading />
      ) : isError ? (
        <div className="text-center py-8">
          <Icon icon="heroicons:exclamation-triangle" className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Posts</h3>
          <p className="text-red-500 mb-4">Something went wrong while loading blog posts</p>
          <Button onClick={() => refetch()} variant="primary">
            Try Again
          </Button>
        </div>
      ) : !posts?.data?.data || posts.data.data.length === 0 ? (
        <div className="text-center py-12">
          <Icon icon="heroicons:document-text" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Blog Posts Found</h3>
          <p className="text-gray-600 mb-6">
            {currentStatus
              ? `No ${currentStatus} blog posts found. Try a different filter or create a new post.`
              : 'No blog posts found. Create your first blog post to get started.'
            }
          </p>
          <Link to="/blog/post/create">
            <Button variant="primary">
              <Icon icon="heroicons:plus" className="mr-2" />
              Create First Post
            </Button>
          </Link>
        </div>
      ) : (
        <BasicTablePage
          title={`${currentStatus ? currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1) : ''} Blog Posts`}
          columns={columns}
          actions={actions}
          data={tableData}
          changePage={changePage}
          currentPage={posts?.data?.meta?.page}
          setFilter={setApiParam}
          totalPages={posts?.data?.meta?.totalPages}
          hideCreateButton={true}
        />
      )}

      {showDeleteModal && (
        <DeleteModal
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
          onSuccess={refetch}
        />
      )}
    </div>
  );
};

export default BlogPostsPage;
