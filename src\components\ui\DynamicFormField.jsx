import React from 'react';
import { Field } from 'formik';
import Input<PERSON><PERSON> from './InputField';
import TextareaField from './TextareaField';

const DynamicFormField = ({ formField, errors, touched, values, setFieldValue }) => {
  const fieldName = `custom_fields.${formField.label_name.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
  const fieldError = errors.custom_fields?.[formField.label_name.toLowerCase().replace(/[^a-z0-9]/g, '_')];
  const fieldTouched = touched.custom_fields?.[formField.label_name.toLowerCase().replace(/[^a-z0-9]/g, '_')];

  const renderField = () => {
    switch (formField.input_type) {
      case 'TEXT':
      case 'EMAIL':
      case 'PHONE':
      case 'URL':
        return (
          <InputField
            name={fieldName}
            type={formField.input_type === 'EMAIL' ? 'email' : formField.input_type === 'PHONE' ? 'tel' : formField.input_type === 'URL' ? 'url' : 'text'}
            placeholder={formField.placeholder}
            required={formField.is_required}
            label={formField.label_name}
            error={fieldError}
          />
        );

      case 'TEXTAREA':
        return (
          <TextareaField
            name={fieldName}
            placeholder={formField.placeholder}
            required={formField.is_required}
            label={formField.label_name}
            rows={3}
            error={fieldError}
          />
        );

      case 'NUMBER':
      case 'RANGE':
        return (
          <InputField
            name={fieldName}
            type="number"
            placeholder={formField.placeholder}
            required={formField.is_required}
            label={formField.label_name}
            min={formField.min_value}
            max={formField.max_value}
            error={fieldError}
          />
        );

      case 'DATE':
        return (
          <InputField
            name={fieldName}
            type="date"
            required={formField.is_required}
            label={formField.label_name}
            error={fieldError}
          />
        );

      case 'TIME':
        return (
          <InputField
            name={fieldName}
            type="time"
            required={formField.is_required}
            label={formField.label_name}
            error={fieldError}
          />
        );

      case 'DATETIME':
        return (
          <InputField
            name={fieldName}
            type="datetime-local"
            required={formField.is_required}
            label={formField.label_name}
            error={fieldError}
          />
        );

      case 'COLOR':
        return (
          <InputField
            name={fieldName}
            type="color"
            required={formField.is_required}
            label={formField.label_name}
            error={fieldError}
          />
        );

      case 'SELECT':
        return (
          <div>
            <label className="block mb-1 font-medium">
              {formField.label_name} {formField.is_required && <span className="text-red-500">*</span>}
            </label>
            {formField.label_subtitle && (
              <p className="text-sm text-gray-600 mb-2">{formField.label_subtitle}</p>
            )}
            <Field
              as="select"
              name={fieldName}
              className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError && fieldTouched ? 'border-red-500' : ''
              }`}
            >
              <option value="">Select an option</option>
              {formField.options?.map((option, index) => (
                <option key={index} value={option}>
                  {option}
                </option>
              ))}
            </Field>
            {fieldError && fieldTouched && (
              <div className="text-red-500 text-sm mt-1">{fieldError}</div>
            )}
          </div>
        );

      case 'RADIO':
        return (
          <div>
            <label className="block mb-2 font-medium">
              {formField.label_name} {formField.is_required && <span className="text-red-500">*</span>}
            </label>
            {formField.label_subtitle && (
              <p className="text-sm text-gray-600 mb-2">{formField.label_subtitle}</p>
            )}
            <div className="space-y-2">
              {formField.options?.map((option, index) => (
                <label key={index} className="flex items-center">
                  <Field
                    type="radio"
                    name={fieldName}
                    value={option}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2">{option}</span>
                </label>
              ))}
            </div>
            {fieldError && fieldTouched && (
              <div className="text-red-500 text-sm mt-1">{fieldError}</div>
            )}
          </div>
        );

      case 'CHECKBOX':
        return (
          <div>
            <label className="block mb-2 font-medium">
              {formField.label_name} {formField.is_required && <span className="text-red-500">*</span>}
            </label>
            {formField.label_subtitle && (
              <p className="text-sm text-gray-600 mb-2">{formField.label_subtitle}</p>
            )}
            <div className="space-y-2">
              {formField.options?.map((option, index) => (
                <label key={index} className="flex items-center">
                  <Field
                    type="checkbox"
                    name={fieldName}
                    value={option}
                    className="form-checkbox h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2">{option}</span>
                </label>
              ))}
            </div>
            {fieldError && fieldTouched && (
              <div className="text-red-500 text-sm mt-1">{fieldError}</div>
            )}
          </div>
        );

      case 'MULTISELECT':
        return (
          <div>
            <label className="block mb-1 font-medium">
              {formField.label_name} {formField.is_required && <span className="text-red-500">*</span>}
            </label>
            {formField.label_subtitle && (
              <p className="text-sm text-gray-600 mb-2">{formField.label_subtitle}</p>
            )}
            <Field
              as="select"
              name={fieldName}
              multiple
              className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError && fieldTouched ? 'border-red-500' : ''
              }`}
              size={Math.min(formField.options?.length || 3, 5)}
            >
              {formField.options?.map((option, index) => (
                <option key={index} value={option}>
                  {option}
                </option>
              ))}
            </Field>
            {fieldError && fieldTouched && (
              <div className="text-red-500 text-sm mt-1">{fieldError}</div>
            )}
          </div>
        );

      case 'FILE':
        return (
          <div>
            <label className="block mb-1 font-medium">
              {formField.label_name} {formField.is_required && <span className="text-red-500">*</span>}
            </label>
            {formField.label_subtitle && (
              <p className="text-sm text-gray-600 mb-2">{formField.label_subtitle}</p>
            )}
            <input
              type="file"
              onChange={(event) => {
                setFieldValue(fieldName, event.currentTarget.files[0]);
              }}
              className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError && fieldTouched ? 'border-red-500' : ''
              }`}
            />
            {fieldError && fieldTouched && (
              <div className="text-red-500 text-sm mt-1">{fieldError}</div>
            )}
          </div>
        );

      default:
        return (
          <InputField
            name={fieldName}
            type="text"
            placeholder={formField.placeholder}
            required={formField.is_required}
            label={formField.label_name}
            error={fieldError}
          />
        );
    }
  };

  return (
    <div className="mb-4">
      {renderField()}
    </div>
  );
};

export default DynamicFormField;
