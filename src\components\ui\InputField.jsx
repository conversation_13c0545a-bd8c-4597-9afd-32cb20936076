import React, { forwardRef } from "react";
import { useField, useFormikContext } from "formik";

const InputField = forwardRef(({
  classLabel = "form-label",
  horizontal,
  label,
  required,
  type,
  error,
  ...props
}, ref) => {
  const [field, meta] = useField(props);
  const { submitCount } = useFormikContext();
  // Show error if field is touched, or form was submitted, or field is required and empty
  const isError = (meta.touched || submitCount > 0) && meta.error;

  // Ensure field value is never null to avoid React warnings
  if (field.value === null) {
    field.value = '';
  }

  return (
    <div className={horizontal ? "flex items-center" : ""}>
      {/* Label styling */}
      {label && (
        <label
          htmlFor={props.id || props.name}
          className={`block capitalize ${classLabel} ${
            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
          }`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className={horizontal ? "flex-1" : ""}>
        <input
          {...field}
          {...props}
          type={type === "number" ? "text" : type}
          className={`appearance-none border rounded h-10 w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
            isError ? "border-red-500" : ""
          }`}
          ref={ref}
          onKeyDown={type === "number" ? (e) => {
            if (e.key === "ArrowUp" || e.key === "ArrowDown") {
              e.preventDefault();
            }
          } : undefined}
        />
        {isError && (
          <span className="text-red-500 text-xs block mt-1">
            {meta.error}
          </span>
        )}
      </div>
    </div>
  );
});

export default InputField;