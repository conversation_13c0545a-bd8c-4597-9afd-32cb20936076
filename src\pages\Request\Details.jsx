import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";
import Sellers from "./Sellers";

// New Components
import ImageGallery from "@/components/request/ImageGallery";
import DocumentList from "@/components/request/DocumentList";
import RequestInfo from "@/components/request/RequestInfo";
import StatusHistory from "@/components/request/StatusHistory";
import AssignedSellers from "@/components/request/AssignedSellers";
import BuyerInfo from "@/components/request/BuyerInfo";

const RequestDetailsPage = () => {
    const { id } = useParams();
    const [showApproveModal, setShowApproveModal] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const { data: responseData, isLoading, isError } = useGetApiQuery(`admin/requests/${id}`);

    if (isLoading) return <Loading />;
    if (isError) return <div className="text-red-500">Error fetching data</div>;

    const request = responseData.data;
    const attachments = request.request_attachments || [];

    // Helper functions
    const isImageFile = (filePath) => {
        if (!filePath) return false;
        const extension = filePath.split('.').pop().toLowerCase();
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
    };

    const handleDocumentClick = (filePath) => {
        if (!filePath) return;
        const fileUrl = `${import.meta.env.VITE_ASSET_HOST_URL}${filePath}`;
        window.open(fileUrl, '_blank');
    };

    // Filter attachments
    const imageAttachments = attachments.filter(att => isImageFile(att.file_path));
    const documentAttachments = attachments.filter(att => !isImageFile(att.file_path));

    return (
        <div className="">
     
            {/* Main Card */}
            <div className="bg-white rounded-xl shadow-md p-6 grid md:grid-cols-2 gap-6">
                {/* Left Column */}
                <div>
                    
                    {/* Buyer Information */}
                    {request.buyer && (
                        <div className="mb-6">
                            <BuyerInfo buyer={request.buyer} />
                        </div>
                    )}
                    <ImageGallery
                        imageAttachments={imageAttachments}
                        selectedImageIndex={selectedImageIndex}
                        setSelectedImageIndex={setSelectedImageIndex}
                        request={request}
                        handleDocumentClick={handleDocumentClick}
                    />
                    { request?.file &&
                        <DocumentList
                            documentAttachments={documentAttachments}
                            handleDocumentClick={handleDocumentClick}
                        />
                    }
                </div>

                {/* Right Column */}
                <RequestInfo
                    request={request}
                    setShowApproveModal={setShowApproveModal}
                    setShowRejectModal={setShowRejectModal}
                />
            </div>

       

            {/* Status History and Assigned Sellers */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Assigned Sellers - Left Side (col-span-2) */}
                <div className="md:col-span-2">
                    <AssignedSellers assignedSellers={request.assigned_sellers} />
                </div>

                {/* Status History - Right Side (col-span-1) */}
                <div className="md:col-span-1">
                    <StatusHistory statusHistory={request.request_statuses} />
                </div>
            </div>

            {/* Seller Assignment Component */}
            {request.status?.toLowerCase() === 'approved' && (
                <div className="mt-6">
                    <Sellers requestId={request.id} assignedSellers={request.assigned_sellers}/>
                </div>
            )}

            {/* Modals */}
            {showApproveModal && (
                <ApproveModal showApproveModal={showApproveModal} setShowApproveModal={setShowApproveModal} data={request} />
            )}
            {showRejectModal && (
                <RejectModal showRejectModal={showRejectModal} setShowRejectModal={setShowRejectModal} data={request} />
            )}
        </div>
    );
};

export default RequestDetailsPage;
