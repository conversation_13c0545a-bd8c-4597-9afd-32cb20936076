import React, { useState } from "react";
import Card from "@/components/ui/Card";
import GlobalFilter from "./GlobalFilter";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import Pagination from "./pagination";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import Loading from "@/components/Loading";

const MenuSettingTable = ({
  tableHeaderExtra = null,
  title,
  createButton,
  createPage,
  editPage,
  actions = [],
  columns,
  changePage,
  data,
  filter,
  setFilter,
  currentPage,
  totalPages,
  submitForm,
  loading,
}) => {
  const [gridView, setGridView] = useState(false);
  const dispatch = useDispatch();
  const { showModal, showEditModal } = useSelector(
    (state) => state.commonReducer
  );
  const handlePageChange = (value) => {
    changePage(`?page=${value}`);
  };
  const openCreateModal = () => {
    dispatch(setShowModal(true));
  };
  return (
    <div className="">
      <div>
        <Card >
          <div className="md:flex justify-between items-center mb-5 border-b pb-3">
            <h4 className="card-title break-words overflow-hidden">{title}</h4>
            <div className="flex gap-2">
              {tableHeaderExtra}
              <GlobalFilter filter={filter} setFilter={setFilter} />
              {createButton && (
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => openCreateModal()}
                >
                  {createButton}
                </button>
              )}
              {showModal && createPage}
              {showEditModal && editPage}
            </div>
          </div>
          <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
            <thead>
              <tr>
              <td>
            <div className="flex justify-end">
              <div className="flex items-center divide-x mb-5 rounded border">
                <button
                  onClick={() => setGridView(true)}
                  className={`p-2 px-3.5 rounded-l transition-all duration-300 ${
                    gridView ? "bg-gray-300" : "bg-gray-100"
                  }`}
                >
                  <Icon icon="nrk:category" className={"text-xl"} />
                </button>
                <button
                  onClick={() => setGridView(false)}
                  className={`p-2 px-3.5 rounded-r transition-all duration-300 ${
                    !gridView ? "bg-gray-300" : "bg-gray-100"
                  }`}
                >
                  <Icon icon="ph:rows-bold" className={"text-xl"} />
                </button>
              </div>
            </div>
            </td>
            </tr>
            </thead>
              <tbody
                className={`bg-white transition-all duration-500 ease-in-out transform ${
                  gridView
                    ? "grid gap-5 grid-cols-2 opacity-100 scale-100"
                    : "opacity-100 scale-98"
                }`}
              >
                {data?.map((row, dataIndex) => (
                  <tr
                    key={dataIndex}
                    className={`border rounded-lg bg-gray-50 border-gray-200 flex justify-between items-center transition-all duration-500 ease-in-out ${
                      gridView
                        ? "scale-100 opacity-100"
                        : "scale-98 opacity-100"
                    } ${!gridView ? "mb-6" : ""}`}
                  >
                    {columns.map(
                      (column, index) =>
                        column.field && (
                          <td key={index} className="table-td p-4">
                            {row[column.field]}
                          </td>
                        )
                    )}
                    {actions.length > 0 && (
                      <td colSpan={"3"} className="table-td ">
                        <Dropdown
                          classMenuItems="right-0 w-[140px] top-[110%]"
                          label={
                            <span className="text-xl text-center block w-full">
                              <Icon icon="heroicons-outline:dots-vertical" />
                            </span>
                          }
                        >
                          <div className="divide-y divide-slate-100 dark:divide-slate-800">
                            {actions
                              .filter(
                                (item) =>
                                  !(item.name === "Delete" && row.course_count > 0)
                              )
                              .map((item, i) => (
                                <Menu.Item key={i}>
                                  <div
                                    className={`w-full border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm last:mb-0 cursor-pointer first:rounded-t last:rounded-b flex space-x-2 items-center rtl:space-x-reverse`}
                                    onClick={() => item.onClick(dataIndex)}
                                  >
                                    <span className="text-base">
                                      <Icon icon={item.icon} />
                                    </span>
                                    <span>{item.name}</span>
                                  </div>
                                </Menu.Item>
                              ))}
                          </div>
                        </Dropdown>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>

          </table>
          {totalPages > 0 && (
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          )}
        </Card>
      </div>
    </div>
  );
};

export default MenuSettingTable;