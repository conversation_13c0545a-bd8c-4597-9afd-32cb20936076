import React from "react";
import { Link } from "react-router-dom";
import Icon from "@/components/ui/Icon";

const SellerInfo = ({ seller }) => {
  if (!seller) return null;

  // Get avatar URL or use default
  const getAvatarUrl = () => {
    if (seller.profile_picture_url) {
      return `${import.meta.env.VITE_ASSET_HOST_URL}${seller.profile_picture_url}`;
    }
    return "https://ui-avatars.com/api/?name=" + encodeURIComponent(`${seller.first_name} ${seller.last_name}`);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'danger';
      case 'pending':
        return 'warning';
      default:
        return 'gray';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Seller Information</h3>
      
      <div className="flex items-start space-x-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <img 
            src={getAvatarUrl()} 
            alt={`${seller.first_name} ${seller.last_name}`} 
            className="h-16 w-16 rounded-full object-cover border-2 border-gray-200"
          />
        </div>
        
        {/* Seller Details */}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <Link 
              to={`/user/${seller.id}`} 
              className="text-lg font-medium text-blue-600 hover:text-blue-800 hover:underline"
            >
              {seller.first_name} {seller.last_name}
            </Link>
            <span className={`px-2 py-1 text-xs rounded-full bg-${getStatusColor(seller.status)}-100 text-${getStatusColor(seller.status)}-800`}>
              {seller.status}
            </span>
          </div>
          
          <div className="mt-2 space-y-1 text-sm text-gray-600">
            <div className="flex items-center">
              <Icon icon="heroicons-outline:mail" className="h-4 w-4 mr-2" />
              <a href={`mailto:${seller.email}`} className="hover:text-blue-600">{seller.email}</a>
            </div>
            
            <div className="flex items-center">
              <Icon icon="heroicons-outline:phone" className="h-4 w-4 mr-2" />
              <a href={`tel:${seller.phone_number}`} className="hover:text-blue-600">{seller.phone_number}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerInfo;
