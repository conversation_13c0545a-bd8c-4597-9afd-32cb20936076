import React, { useState, useRef } from 'react';
import { Formik, Form } from 'formik';
import { useUpdateApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import { useNavigate, useParams } from 'react-router-dom';
import { skipToken } from "@reduxjs/toolkit/query";
import InputField from "@/components/ui/InputField";
import TextareaField from "@/components/ui/TextareaField";
import Card from "@/components/ui/Card";
import { toast } from 'react-toastify';
import Icon from "@/components/ui/Icon";
import DynamicFormField from '@/components/ui/DynamicFormField';
import Loading from '@/components/Loading';
import { getInitialValues, validationSchema } from "./FormSettings";

const EditRequest = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const formikRef = useRef();
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);

  // Fetch request data
  const { data: requestData, isLoading: isRequestLoading } = useGetApiQuery(
    id ? `/admin/requests/${id}` : skipToken
  );

  // Fetch subcategory form fields
  const { 
    data: subcategoryData, 
    isLoading: isLoadingSubcategory 
  } = useGetApiQuery(
    requestData?.data?.sub_category_id ? `/admin/subcategories/${requestData.data.sub_category_id}` : skipToken
  );

  const [updateApi, { isLoading: isUpdateLoading }] = useUpdateApiMutation();

  const handleSubmit = async (values, { setErrors }) => {
    setFormSubmitting(true);
    
    try {
      const payload = {
        title: values.title,
        short_description: values.short_description || null,
        description: values.description || null,
        quantity: parseInt(values.quantity) || 1,
        budget_min: parseFloat(values.budget_min) || null,
        budget_max: parseFloat(values.budget_max) || null,
        deadline: values.deadline || null,
        urgency: values.urgency,
        request_type: values.request_type,
        location: values.location || null,
        additional_info: values.additional_info || null,
        custom_fields: values.custom_fields || {}
      };

      const response = await updateApi({
        end_point: `/admin/requests/${id}`,
        body: payload
      });

      if (response.error) {
        console.error('Error:', response.error);
        if (response.error.data && response.error.data.errors) {
          setErrors(response.error.data.errors);
        }
        toast.error(response.error.data?.message || 'Failed to update request');
      } else {
        toast.success('Request updated successfully');
        navigate('/requests');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('An error occurred while updating the request');
    } finally {
      setFormSubmitting(false);
    }
  };

  if (isRequestLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (!requestData?.data) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Icon icon="heroicons:exclamation-triangle" className="w-16 h-16 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Request Not Found</h2>
        <p className="text-gray-600 mb-4">The request you're looking for doesn't exist.</p>
        <Button
          variant="primary"
          onClick={() => navigate('/requests')}
        >
          Back to Requests
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Edit Request</h1>
          <p className="text-sm text-gray-600 mt-1">
            Update request details and custom fields
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/requests')}
            className="h-9 px-4 flex items-center"
          >
            <Icon icon="heroicons:arrow-left" className="mr-2 w-4 h-4" /> Back
          </Button>
        </div>
      </div>

      {/* Form */}
      <Card className="max-w-4xl">
        <div className="p-6">
          <Formik
            innerRef={formikRef}
            initialValues={getInitialValues(requestData?.data)}
            enableReinitialize
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            validateOnBlur
            validateOnChange
            validateOnMount
          >
            {({ values, setFieldValue, handleBlur, touched, errors, setTouched, validateForm }) => (
              <Form className="space-y-6">
                {/* Error Summary */}
                {submitAttempted && Object.keys(errors).length > 0 && (
                  <Card className="border-red-200 bg-red-50">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-0.5">
                        <Icon icon="heroicons:exclamation-circle" className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                          Please fix the following errors:
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                          <ul className="list-disc list-inside space-y-1">
                            {Object.entries(errors).map(([field, error]) => (
                              <li key={field}>
                                {field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}: {error}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </Card>
                )}

                {/* Basic Information */}
                <Card>
                  <div className="p-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                    <p className="text-sm text-gray-600">Update the basic details of the request</p>
                  </div>
                  <div className="p-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Title */}
                      <div className="md:col-span-2">
                        <InputField
                          name="title"
                          label="Title"
                          required
                          placeholder="Enter request title"
                        />
                      </div>

                      {/* Short Description */}
                      <div className="md:col-span-2">
                        <TextareaField
                          name="short_description"
                          label="Short Description"
                          placeholder="Brief description of the request"
                          rows={2}
                        />
                      </div>

                      {/* Description */}
                      <div className="md:col-span-2">
                        <TextareaField
                          name="description"
                          label="Description"
                          placeholder="Detailed description of the request"
                          rows={4}
                        />
                      </div>

                      {/* Quantity */}
                      <div>
                        <InputField
                          name="quantity"
                          label="Quantity"
                          type="number"
                          min="1"
                          placeholder="1"
                        />
                      </div>

                      {/* Budget Min */}
                      <div>
                        <InputField
                          name="budget_min"
                          label="Minimum Budget"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                        />
                      </div>

                      {/* Budget Max */}
                      <div>
                        <InputField
                          name="budget_max"
                          label="Maximum Budget"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                        />
                      </div>

                      {/* Deadline */}
                      <div>
                        <InputField
                          name="deadline"
                          label="Deadline"
                          type="date"
                        />
                      </div>

                      {/* Urgency */}
                      <div>
                        <label className="block mb-1 font-medium text-sm text-gray-700">Urgency</label>
                        <select
                          name="urgency"
                          value={values.urgency}
                          onChange={(e) => setFieldValue('urgency', e.target.value)}
                          className="form-control"
                        >
                          <option value="Low">Low</option>
                          <option value="Normal">Normal</option>
                          <option value="High">High</option>
                          <option value="Urgent">Urgent</option>
                        </select>
                      </div>

                      {/* Request Type */}
                      <div>
                        <label className="block mb-1 font-medium text-sm text-gray-700">Request Type</label>
                        <select
                          name="request_type"
                          value={values.request_type}
                          onChange={(e) => setFieldValue('request_type', e.target.value)}
                          className="form-control"
                        >
                          <option value="General">General</option>
                          <option value="Service">Service</option>
                          <option value="Product">Product</option>
                          <option value="Consultation">Consultation</option>
                        </select>
                      </div>

                      {/* Location */}
                      <div className="md:col-span-2">
                        <InputField
                          name="location"
                          label="Location"
                          placeholder="Enter location"
                        />
                      </div>

                      {/* Additional Info */}
                      <div className="md:col-span-2">
                        <TextareaField
                          name="additional_info"
                          label="Additional Information"
                          placeholder="Any additional information"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Dynamic Form Fields */}
                {isLoadingSubcategory ? (
                  <Card>
                    <div className="p-4 flex justify-center">
                      <Loading />
                    </div>
                  </Card>
                ) : subcategoryData?.data?.form_fields && subcategoryData.data.form_fields.length > 0 && (
                  <Card>
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="text-lg font-medium text-gray-900">Category Specific Fields</h3>
                      <p className="text-sm text-gray-600">Fields specific to {subcategoryData.data.title}</p>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {subcategoryData.data.form_fields
                          .sort((a, b) => a.sort_order - b.sort_order)
                          .map((formField) => (
                            <div key={formField.id} className={formField.input_type === 'TEXTAREA' ? 'md:col-span-2' : ''}>
                              <DynamicFormField
                                formField={formField}
                                errors={errors}
                                touched={touched}
                                values={values}
                                setFieldValue={setFieldValue}
                              />
                            </div>
                          ))}
                      </div>
                    </div>
                  </Card>
                )}

                {/* Form Actions */}
                <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/requests')}
                    disabled={formSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    variant="primary"
                    isLoading={formSubmitting || isUpdateLoading}
                    onClick={async () => {
                      setSubmitAttempted(true);
                      const touchedFields = {};
                      Object.keys(values).forEach(key => {
                        touchedFields[key] = true;
                      });
                      setTouched(touchedFields);

                      const errors = await validateForm();
                      if (Object.keys(errors).length === 0) {
                        formikRef.current.handleSubmit();
                      }
                    }}
                  >
                    Update Request
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Card>
    </div>
  );
};

export default EditRequest;
