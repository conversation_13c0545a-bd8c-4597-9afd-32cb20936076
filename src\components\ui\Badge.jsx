import React from "react";
import Icon from "@/components/ui/Icon";

const Badge = ({
  className = "",
  label,
  icon,
  children,
  variant = "default",
  size = "sm",
}) => {
  const variantClasses = {
    default: "bg-gray-100 text-gray-800",
    primary: "bg-blue-100 text-blue-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-yellow-100 text-yellow-800",
    danger: "bg-red-100 text-red-800",
    info: "bg-indigo-100 text-indigo-800",
  };

  const sizeClasses = {
    xs: "px-1.5 py-0.5 text-xs",
    sm: "px-2.5 py-0.5 text-xs",
    md: "px-3 py-1 text-sm",
    lg: "px-3.5 py-1.5 text-sm",
  };

  const baseClass = `inline-flex items-center rounded-full font-medium ${variantClasses[variant]} ${sizeClasses[size]}`;

  return (
    <span className={`${baseClass} ${className}`}>
      {!children && (
        <span className="inline-flex items-center">
          {icon && (
            <span className="inline-block ltr:mr-1 rtl:ml-1">
              <Icon icon={icon} />
            </span>
          )}
          {label}
        </span>
      )}
      {children && <span className="inline-flex items-center">{children}</span>}
    </span>
  );
};

export default Badge;
