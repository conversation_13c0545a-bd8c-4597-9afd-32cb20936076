import React, { useState } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Button from "@/components/ui/Button";
import Loading from "@/components/Loading";
import Delete from "./Delete";
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import Icon from "@/components/ui/Icon";

const SubscriptionDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const { data: subscription, isLoading, error } = useGetApiQuery(`/admin/subscriptions/${id}`);

  if (isLoading) return <Loading />;
  if (error) return <div className="text-red-500">Error loading subscription details</div>;

  const subscriptionData = subscription?.data;

  const formatFeatures = (features) => {
    if (!features || typeof features !== 'object') return [];

    return Object.entries(features).map(([key, value]) => ({
      key: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            icon="heroicons:chevron-left"
            variant="outline"
            onClick={() => navigate('/subscriptions')}
            className="h-10 w-10 p-0"
          />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {subscriptionData?.name}
            </h1>
            <p className="text-gray-500 dark:text-gray-400">
              Subscription Plan Details
            </p>
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            icon="heroicons:pencil-square"
            onClick={() => navigate(`/subscription/edit/${id}`)}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            Edit Plan
          </Button>
          <Button
            icon="heroicons:trash"
            onClick={() => setShowDeleteModal(true)}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            Delete
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Plan Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info Card */}
          <Card title="Plan Information" className="h-fit">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Plan Name</label>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {subscriptionData?.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Price</label>
                  <p className="text-lg font-semibold text-green-600">
                    ${parseFloat(subscriptionData?.price || 0).toFixed(2)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</label>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {subscriptionData?.duration_days} days
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">User Type</label>
                  <div className="mt-1">
                    <Badge
                      className={
                        subscriptionData?.user_type === 'BUYER'
                          ? 'bg-blue-100 text-blue-800'
                          : subscriptionData?.user_type === 'SELLER'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-gray-100 text-gray-800'
                      }
                    >
                      {subscriptionData?.user_type}
                    </Badge>
                  </div>
                </div>
              </div>

              {subscriptionData?.description && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Description</label>
                  <p className="text-gray-900 dark:text-white mt-1">
                    {subscriptionData.description}
                  </p>
                </div>
              )}
            </div>
          </Card>

          {/* Limits Card */}
          <Card title="Plan Limits" className="h-fit">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {subscriptionData?.max_requests || '∞'}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Max Requests</div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {subscriptionData?.max_offers || '∞'}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Max Offers</div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {subscriptionData?.max_orders || '∞'}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Max Orders</div>
              </div>
            </div>
          </Card>

          {/* Features Card */}
          {subscriptionData?.features && (
            <Card title="Plan Features" className="h-fit">
              <div className="space-y-3">
                {formatFeatures(subscriptionData.features).map((feature, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <span className="text-gray-700 dark:text-gray-300">{feature.key}</span>
                    <span className="font-medium text-gray-900 dark:text-white">{feature.value}</span>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Card */}
          <Card title="Plan Status" className="h-fit">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-700 dark:text-gray-300">Status</span>
                <Badge
                  className={
                    subscriptionData?.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }
                >
                  {subscriptionData?.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-700 dark:text-gray-300">Featured</span>
                <Badge
                  className={
                    subscriptionData?.is_featured
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }
                >
                  {subscriptionData?.is_featured ? 'Featured' : 'Regular'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-700 dark:text-gray-300">Subscribers</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {subscriptionData?._count?.user_subscriptions || 0}
                </span>
              </div>
            </div>
          </Card>

          {/* Timestamps Card */}
          <Card title="Timestamps" className="h-fit">
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {subscriptionData?.created_at ? new Date(subscriptionData.created_at).toLocaleDateString() : 'N/A'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {subscriptionData?.updated_at ? new Date(subscriptionData.updated_at).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Delete Modal */}
      {showDeleteModal && (
        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={subscriptionData}
        />
      )}
    </div>
  );
};

export default SubscriptionDetails;
