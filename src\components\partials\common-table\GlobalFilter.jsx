import React, { useState, useEffect, useCallback } from "react";
import Textinput from "@/components/ui/Textinput";
import { debounce } from "lodash";
import { useLocation } from "react-router-dom";

const GlobalFilter = ({ filter, setFilter }) => {
  const location = useLocation();

  // Extract search query from URL if it exists
  const searchParams = new URLSearchParams(location.search);
  const searchFromUrl = searchParams.get('search') || '';

  // Initialize with search from URL or filter prop
  const [value, setValue] = useState(searchFromUrl || filter || '');

  // Update value when URL changes
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const searchFromUrl = searchParams.get('search') || '';
    if (searchFromUrl !== value) {
      setValue(searchFromUrl);
    }
  }, [location.search]);

  // Wrap debounced function in useCallback to maintain the same instance
  const debouncedFilter = useCallback(
    debounce((value) => {
      // Determine if we need to use ? or & for the search parameter
      const hasQuestionMark = location.search.includes('?');
      const prefix = hasQuestionMark && !location.search.includes('search=') ? '&' : '?';

      setFilter(value ? `${prefix}search=${value}` : '');
    }, 500),
    [setFilter, location.search]
  );

  // Update the debounced value whenever input changes
  const onChange = (e) => {
    const newValue = e.target.value;
    setValue(newValue);
    debouncedFilter(newValue);
  };

  // Cleanup debounce on component unmount
  useEffect(() => {
    return () => {
      debouncedFilter.cancel();
    };
  }, [debouncedFilter]);

  return (
    <div>
      <Textinput
        value={value}
        onChange={onChange}
        placeholder="search..."
      />
    </div>
  );
};

export default GlobalFilter;
