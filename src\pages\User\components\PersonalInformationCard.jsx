import React from 'react';

const PersonalInformationCard = ({ user, formatDate }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-4">Basic Details</h4>
            <div className="space-y-4">
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Full Name</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.first_name} {user.last_name}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Gender</div>
                <div className="w-2/3 text-sm font-medium text-gray-900 capitalize">{user.gender || 'N/A'}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Date of Birth</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{formatDate(user.date_of_birth)}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Father's Name</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.father_name || 'N/A'}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Mother's Name</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.mother_name || 'N/A'}</div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-4">Contact & Identification</h4>
            <div className="space-y-4">
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Email</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.email}</div>
              </div>
              {user.business_name && (
                <div className="flex">
                  <div className="w-1/3 text-sm text-gray-500">Business Name</div>
                  <div className="w-2/3 text-sm font-medium text-gray-900">{user.business_name}</div>
                </div>
              )}
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Phone</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.phone_number}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Address</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.address || 'N/A'}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">National ID</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.national_id_number || 'N/A'}</div>
              </div>
              <div className="flex">
                <div className="w-1/3 text-sm text-gray-500">Passport</div>
                <div className="w-2/3 text-sm font-medium text-gray-900">{user.passport_number || 'N/A'}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonalInformationCard;
