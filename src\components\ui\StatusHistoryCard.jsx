import React from "react";
import Badge from "@/components/ui/Badge";

const statusColors = {
    Pending: "bg-yellow-100 text-yellow-800",
    Approved: "bg-green-100 text-green-800",
    Rejected: "bg-red-100 text-red-800",
};

const StatusHistoryCard = ({ status, previousStatus, reason, updatedBy, updatedAt }) => {
    return (
        <div className="border border-gray-200 rounded-xl p-4 shadow-sm bg-white space-y-2">
            <div className="flex flex-wrap justify-between items-center">
                <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Changed from</span>
                    <Badge className={`${statusColors[previousStatus] || 'bg-gray-100 text-gray-800'} px-2 py-0.5 rounded-full text-xs`}>
                        {previousStatus}
                    </Badge>
                    <span className="text-sm text-gray-600">to</span>
                    <Badge className={`${statusColors[status] || 'bg-gray-100 text-gray-800'} px-2 py-0.5 rounded-full text-xs`}>
                        {status}
                    </Badge>
                </div>
                <span className="text-xs text-gray-400">{updatedAt}</span>
            </div>

            {reason && (
                <div className="text-sm text-gray-700">
                    <strong>Reason:</strong> {reason}
                </div>
            )}

            <div className="text-sm text-gray-500">
                <strong>Updated by:</strong> {updatedBy}
            </div>
        </div>
    );
};

export default StatusHistoryCard;
