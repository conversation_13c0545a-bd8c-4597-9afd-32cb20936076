import * as Yup from 'yup';

export const getInitialValues = (data = null) => {
  return {
    title: data?.title || '',
    short_description: data?.short_description || '',
    description: data?.description || '',
    category_id: data?.category_id || '',
    sub_category_id: data?.sub_category_id || '',
    quantity: data?.quantity || 1,
    budget_min: data?.budget_min || '',
    budget_max: data?.budget_max || '',
    deadline: data?.deadline ? new Date(data.deadline).toISOString().split('T')[0] : '',
    urgency: data?.urgency || 'Normal',
    request_type: data?.request_type || 'General',
    location: data?.location || '',
    additional_info: data?.additional_info || '',
    custom_fields: data?.custom_fields || {}
  };
};

export const validationSchema = Yup.object().shape({
  title: Yup.string()
    .required('Title is required')
    .min(3, 'Title must be at least 3 characters')
    .max(200, 'Title must not exceed 200 characters'),
  short_description: Yup.string()
    .max(500, 'Short description must not exceed 500 characters'),
  description: Yup.string()
    .max(2000, 'Description must not exceed 2000 characters'),
  quantity: Yup.number()
    .positive('Quantity must be positive')
    .integer('Quantity must be an integer'),
  budget_min: Yup.number()
    .min(0, 'Minimum budget must be non-negative'),
  budget_max: Yup.number()
    .min(0, 'Maximum budget must be non-negative')
    .test('budget-range', 'Maximum budget must be greater than or equal to minimum budget', function(value) {
      const { budget_min } = this.parent;
      if (value && budget_min && parseFloat(value) < parseFloat(budget_min)) {
        return false;
      }
      return true;
    }),
  deadline: Yup.date(),
  urgency: Yup.string()
    .oneOf(['Low', 'Normal', 'High', 'Urgent'], 'Invalid urgency level'),
  request_type: Yup.string()
    .oneOf(['General', 'Service', 'Product', 'Consultation'], 'Invalid request type'),
  location: Yup.string()
    .max(200, 'Location must not exceed 200 characters'),
  additional_info: Yup.string()
    .max(1000, 'Additional info must not exceed 1000 characters'),
});

export const mergeValidationSchema = Yup.object().shape({
  title: Yup.string()
    .required('Title is required')
    .min(3, 'Title must be at least 3 characters')
    .max(200, 'Title must not exceed 200 characters'),
  short_description: Yup.string()
    .max(500, 'Short description must not exceed 500 characters'),
  description: Yup.string()
    .max(2000, 'Description must not exceed 2000 characters'),
  category_id: Yup.string()
    .required('Category is required'),
  sub_category_id: Yup.string()
    .required('Sub-category is required'),
  quantity: Yup.number()
    .positive('Quantity must be positive')
    .integer('Quantity must be an integer'),
  budget_min: Yup.number()
    .min(0, 'Minimum budget must be non-negative'),
  budget_max: Yup.number()
    .min(0, 'Maximum budget must be non-negative')
    .test('budget-range', 'Maximum budget must be greater than or equal to minimum budget', function(value) {
      const { budget_min } = this.parent;
      if (value && budget_min && parseFloat(value) < parseFloat(budget_min)) {
        return false;
      }
      return true;
    }),
  deadline: Yup.date(),
  urgency: Yup.string()
    .required('Urgency is required')
    .oneOf(['Low', 'Normal', 'High', 'Urgent'], 'Invalid urgency level'),
  request_type: Yup.string()
    .required('Request type is required')
    .oneOf(['General', 'Service', 'Product', 'Consultation'], 'Invalid request type'),
  location: Yup.string()
    .max(200, 'Location must not exceed 200 characters'),
  additional_info: Yup.string()
    .max(1000, 'Additional info must not exceed 1000 characters'),
});

export const getMergeInitialValues = () => {
  return {
    title: '',
    short_description: '',
    description: '',
    category_id: '',
    sub_category_id: '',
    quantity: 1,
    budget_min: '',
    budget_max: '',
    deadline: '',
    urgency: 'Normal',
    request_type: 'General',
    location: '',
    additional_info: '',
  };
};

export const URGENCY_OPTIONS = [
  { value: 'Low', label: 'Low' },
  { value: 'Normal', label: 'Normal' },
  { value: 'High', label: 'High' },
  { value: 'Urgent', label: 'Urgent' }
];

export const REQUEST_TYPE_OPTIONS = [
  { value: 'General', label: 'General' },
  { value: 'Service', label: 'Service' },
  { value: 'Product', label: 'Product' },
  { value: 'Consultation', label: 'Consultation' }
];
