import { apiSlice } from "../apiSlice";
export const commonApi = apiSlice.injectEndpoints({
  reducerPath: "commonSliceApi",
  tagTypes: ["LIST_API"],
  endpoints: (builder) => ({

    getApi: builder.query({
      query: (url) => {
        return {
          url: url,
          method: "GET",
        };
      },
      providesTags: ["LIST_API"],
    }),

    postApi: builder.mutation({
      query: (data) => {
        return {
          url: data.end_point,
          method: "POST",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),

    updateApi: builder.mutation({
      query: (data) => {
        return {
          url: data.end_point,
          method: "PUT",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),

    updateFormDataApi: builder.mutation({
      query: (data) => {
        return {
          url: data.end_point,
          method: "POST",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),
    deleteApi: builder.mutation({
      query: (data) => {
        // data.body._method = "DELETE";
        return {
          url: data.end_point,
          method: "DELETE",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),
  }),
});

export const {
  useGetApiQuery,
  usePostApiMutation,
  useUpdateApiMutation,
  useDeleteApiMutation
 } = commonApi;
