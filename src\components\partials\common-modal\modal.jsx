import React, { useState } from "react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import { Formik, Form, Field } from 'formik';

const ModalPage = ({label, createPage, initialValues, showModal, validationSchema }) => {
    const [activeModal, setActiveModal] = useState(showModal);
    console.log(initialValues);
    
    const onSubmit = async (values, { resetForm }) => {
        console.log(values);
        // setActiveModal(false);
    }

  return (
    <div>
      <Modal
        activeModal={activeModal}
        onClose={() => setActiveModal(false)}
        title={label}
        footer={
          <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setActiveModal(false)}
          />
        }
      >
        <Formik 
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}>
        {({ values,
         errors,
         touched,
         handleChange,
         handleBlur,
         handleSubmit,
         isSubmitting, }) => (
          <Form>
                    
            {createPage}
            <div className="ltr:text-right rtl:text-left mt-5">
                <Button
                    type="submit"
                    className="btn text-center btn-primary"
                >
                    Submit
                </Button>
                </div>
          
          </Form>
        )}
        </Formik>
      </Modal>
    </div>

    );
};

export default ModalPage;
