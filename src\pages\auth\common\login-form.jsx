import React, { useState } from "react";
import { Formik, Form, Field } from "formik";
import * as yup from "yup";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useLoginMutation } from "@/store/api/auth/authApiSlice";
import { useDispatch } from "react-redux";
import { setUser, setToken } from "@/store/api/auth/authSlice";
import { Icon } from "@iconify/react";

// Validation schema
const schema = yup.object({
  email: yup.string().required("Email is required"),
  password: yup.string().required("Password is required"),
});

const LoginForm = ({ handleResponse, setError }) => {
  const navigate = useNavigate();
  const [checked, setChecked] = useState(true);
  const [showPassword, setShowPassword] = useState(false);

  const initialValues = {
    email: "",
    password: "",
    remember: false,
  };

  const [login, { isLoading, isError, error, isSuccess }] = useLoginMutation();

  const dispatch = useDispatch();
  const handleSubmit = async (values) => {
    values.checked = checked;
    try {
      const response = await login(values);

      if (response?.data?.data) {
        const { accessToken, refreshToken, user } = response.data.data;

        // Set expiration date for cookies (e.g., 1 day for access token, 7 days for refresh token)
        const accessTokenExpiry = new Date();
        accessTokenExpiry.setTime(accessTokenExpiry.getTime() + 24 * 60 * 60 * 1000); // 1 day

        const refreshTokenExpiry = new Date();
        refreshTokenExpiry.setTime(refreshTokenExpiry.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days

        // Store tokens in cookies with expiration
        document.cookie = `accessToken=${accessToken}; path=/; expires=${accessTokenExpiry.toUTCString()}; SameSite=Strict`;
        document.cookie = `refreshToken=${refreshToken}; path=/; expires=${refreshTokenExpiry.toUTCString()}; SameSite=Strict`;

        // Update Redux store
        dispatch(setUser(user));
        dispatch(setToken(accessToken));

        // Navigate to dashboard
        navigate("/dashboard");
      }

    } catch (error) {
      console.error('Login error:', error);
      toast.error(error?.message || "Login failed. Please try again.");
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={schema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched, isSubmitting, setFieldValue }) => (
        <Form className="space-y-4">
          {/* Username Field */}
          <Field name="email">
            {({ field }) => (
              <InputField
                {...field}
                label="Email"
                type="text"
                className="h-[48px]"
                placeholder="Enter Email"
              />
            )}
          </Field>

          {/* Password Field */}
          <div className="relative">
            <Field name="password">
              {({ field }) => (
                <InputField
                  key={showPassword ? "text" : "password"}
                  {...field}
                  label="Password"
                  type={showPassword ? "text" : "password"}
                  className="h-[48px] pr-10"
                  placeholder="Enter Password"
                />
              )}
            </Field>
            <button
              type="button"
              className="absolute right-3 top-[40px] text-gray-500 hover:text-gray-700"
              onClick={() => setShowPassword(!showPassword)}
            >
              <Icon icon={showPassword ? "akar-icons:eye-closed" : "akar-icons:eye"} className="w-5 h-5" />
            </button>
          </div>

          {/* Remember Me Checkbox */}
          {/* <div className="flex justify-between">
            <Checkbox
              value={checked}
              onChange={() => setChecked(!checked)}
              label="Keep me signed in"
            />
          </div> */}

          {/* Submit Button */}
          <Button
            type="submit"
            text="Sign in"
            className="btn btn-dark block w-full text-center"
            isLoading={isLoading}
          />
        </Form>
      )}
    </Formik>
  );
};

export default LoginForm;
