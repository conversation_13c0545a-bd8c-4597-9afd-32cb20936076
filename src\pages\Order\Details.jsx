import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";

// Components
import OrderInfo from "@/components/order/OrderInfo";
import OrderItems from "@/components/order/OrderItems";
import PaymentInfo from "@/components/order/PaymentInfo";
import BuyerInfo from "@/components/request/BuyerInfo";
import SellerInfo from "@/components/order/SellerInfo";
import StatusHistory from "@/components/request/StatusHistory";

const OrderDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const { 
    data: responseData, 
    isLoading, 
    isError, 
    refetch 
  } = useGetApiQuery(`admin/orders/${id}`);

  const order = responseData?.data;

  // Handle back button click
  const handleBackClick = () => {
    navigate(-1);
  };

  if (isLoading) {
    return <Loading />;
  }

  if (isError || !order) {
    return (
      <div className="bg-white p-6 rounded-xl shadow-md">
        <div className="flex items-center justify-between mb-6">
          <Button
            onClick={handleBackClick}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700"
            icon="heroicons-outline:arrow-left"
          >
            Back
          </Button>
        </div>
        <div className="text-center py-8">
          <Icon icon="heroicons-outline:exclamation-circle" className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Order</h2>
          <p className="text-gray-600">Unable to load order details. Please try again later.</p>
          <Button
            onClick={() => refetch()}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <Button
          onClick={handleBackClick}
          className="bg-gray-100 hover:bg-gray-200 text-gray-700"
          icon="heroicons-outline:arrow-left"
        >
          Back
        </Button>
        
        <div className="flex space-x-2">
          {/* Add action buttons here if needed */}
        </div>
      </div>

      {/* Order Information */}
      <OrderInfo order={order} />

      {/* Order Items */}
      <OrderItems items={order.order_items} />

      {/* Buyer and Seller Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <BuyerInfo buyer={order.buyer} />
        <SellerInfo seller={order.seller} />
      </div>

      {/* Offer Information */}
      {order.offer && (
        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Offer Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Offer Details</h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <Icon icon="heroicons-outline:currency-dollar" className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">
                    Price: {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD'
                    }).format(order.offer.price)}
                  </span>
                </div>
                <div className="flex items-center">
                  <Icon icon="heroicons-outline:clock" className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">
                    Delivery Time: {order.offer.delivery_time} days
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Request Information</h3>
              {order.offer.request && (
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Icon icon="heroicons-outline:document-text" className="h-5 w-5 text-gray-400 mr-2" />
                    <Link 
                      to={`/request/${order.offer.request.id}`}
                      className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {order.offer.request.title}
                    </Link>
                  </div>
                  <div className="flex items-start">
                    <Icon icon="heroicons-outline:information-circle" className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                    <span className="text-sm text-gray-700">
                      {order.offer.request.short_description}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Payment Information */}
      <PaymentInfo payments={order.order_payments} />

      {/* Status History and Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          {/* Additional information can go here */}
        </div>
        
        <div className="md:col-span-1">
          <StatusHistory statusHistory={order.order_status_changes} />
        </div>
      </div>
    </div>
  );
};

export default OrderDetailsPage;
