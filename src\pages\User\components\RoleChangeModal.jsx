import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";
import * as Yup from 'yup';
import { Formik, Form, Field } from 'formik';

const RoleChangeModal = ({ showModal, setShowModal, userId, currentRole }) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  // Validation schema
  const validationSchema = Yup.object().shape({
    role: Yup.string()
      .required('Role is required')
      .oneOf(['Buyer', 'Seller', 'Supplier', 'Admin'], 'Invalid role selected')
  });

  // Initial values
  const initialValues = {
    role: currentRole || 'Buyer'
  };

  // Handle form submission
  const handleSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();
      formData.append('role', values.role);
      formData.append('_method', 'PUT');

      const response = await updateApi({
        end_point: `admin/users/${userId}/update-role`,
        body: formData
      });

      if (response.error) {
        toast.error(response.error.data?.message || 'Failed to update role');
      } else {
        toast.success('Role updated successfully');
        setShowModal(false);
        // Reload the page to reflect the changes
        window.location.reload();
      }
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('An error occurred while updating the role');
    }
  };

  return (
    <Modal
      title="Change User Role"
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      className="max-w-md"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ errors, touched }) => (
          <Form className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                User Role
              </label>
              <Field
                as="select"
                name="role"
                className={`mt-1 block w-full pl-3 pr-10 py-2 text-base border ${
                  touched.role && errors.role ? 'border-red-500' : 'border-gray-300'
                } focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md`}
              >
                <option value="Buyer">Buyer</option>
                <option value="Seller">Seller</option>
                <option value="Supplier">Supplier</option>
                <option value="Admin">Admin</option>
              </Field>
              {touched.role && errors.role && (
                <p className="mt-1 text-sm text-red-600">{errors.role}</p>
              )}
            </div>

            <div className="mt-4 text-sm text-gray-500">
              <p>Changing a user's role will affect their permissions and access to different features of the platform.</p>
              <p className="mt-2">Please ensure you understand the implications before proceeding.</p>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowModal(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isLoading}
              >
                Change Role
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default RoleChangeModal;
