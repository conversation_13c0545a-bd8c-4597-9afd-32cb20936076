import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const DeleteModal = ({ showDeleteModal, setShowDeleteModal, data, onSuccess }) => {
    const [deleteApi, { isLoading }] = useDeleteApiMutation();
    
    const onSubmit = async () => {
        try {
            const response = await deleteApi({
                end_point: '/admin/requests/' + data?.id
            });
            
            if (response.error) {
                toast.error(response.error.data?.message || 'Failed to delete request');
            } else {
                setShowDeleteModal(false);
                if (onSuccess) onSuccess();
            }
        } catch (error) {
            console.error('Error deleting request:', error);
            toast.error('An error occurred while deleting the request');
        }
    };

    return (
        <Modal
            title="Delete Request"
            activeModal={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            className="max-w-lg"
        >
            <div className="text-base text-slate-600 dark:text-slate-300">
                <h3 className="text-center text-lg font-medium mb-2">Are you sure?</h3>
                <p className="text-center text-slate-500 text-sm mb-6">
                    You are going to delete the request <b>"{data?.title}"</b>. 
                    This action cannot be undone.
                </p>
                
                <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-red-700">
                                Deleting this request will remove all associated data, including assigned sellers and offers.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                    <Button
                        text="Cancel"
                        className="btn-outline-dark"
                        onClick={() => setShowDeleteModal(false)}
                        disabled={isLoading}
                    />
                    <Button
                        text="Delete"
                        className="btn-danger"
                        onClick={onSubmit}
                        isLoading={isLoading}
                    />
                </div>
            </div>
        </Modal>
    );
};

export default DeleteModal;
