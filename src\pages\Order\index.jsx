import React, { useState, useMemo } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Link } from "react-router-dom";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Loading from "@/components/Loading";
import Badge from "@/components/ui/Badge";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { formatDateTime, getStatusColor } from "@/utils/statusHelpers";

const OrderPage = () => {
  const navigate = useNavigate();
  const { statusParam } = useParams();
  const location = useLocation();
  const queryParam = location.search || "";

  const [apiParam, setApiParam] = useState(queryParam);

  const {
    data: ordersResponse,
    isLoading,
    isError,
    isFetching,
    refetch
  } = useGetApiQuery(`/admin/orders${statusParam && statusParam !== 'all' ? `?status=${statusParam}` : ""}${apiParam}`);

  const handleOrderClick = (id) => {
    navigate(`/order/${id}`);
  };

  const orders = ordersResponse?.data || [];

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getOrderStatusBadge = (status) => {
    if (!status) return null;
    
    const color = getStatusColor(status);
    
    return (
      <Badge className={`bg-${color}-100 text-${color}-800 px-2 py-1 rounded-full text-xs`}>
        {status}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status) => {
    if (!status) return null;
    
    let color;
    switch (status.toLowerCase()) {
      case 'paid':
        color = 'success';
        break;
      case 'pending':
        color = 'warning';
        break;
      case 'refunded':
        color = 'info';
        break;
      case 'failed':
        color = 'danger';
        break;
      default:
        color = 'gray';
    }
    
    return (
      <Badge className={`bg-${color}-100 text-${color}-800 px-2 py-1 rounded-full text-xs`}>
        {status}
      </Badge>
    );
  };

  const tableData = useMemo(() => {
    return orders.map((order) => {
      return {
        id: (
          <span className="text-sm font-medium text-blue-600 cursor-pointer" onClick={() => handleOrderClick(order.id)}>
            #{order.id.substring(0, 8)}
          </span>
        ),
        buyer: (
          <div className="flex items-center">
            <div className="flex-shrink-0 h-8 w-8">
              <img
                className="h-8 w-8 rounded-full"
                src={order.buyer?.profile_picture_url ? `${import.meta.env.VITE_ASSET_HOST_URL}${order.buyer.profile_picture_url}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(`${order.buyer?.first_name || ''} ${order.buyer?.last_name || ''}`)}`}
                alt={`${order.buyer?.first_name || ''} ${order.buyer?.last_name || ''}`}
              />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {order.buyer?.first_name} {order.buyer?.last_name}
              </p>
              <p className="text-xs text-gray-500">{order.buyer?.email}</p>
            </div>
          </div>
        ),
        seller: (
          <div className="flex items-center">
            <div className="flex-shrink-0 h-8 w-8">
              <img
                className="h-8 w-8 rounded-full"
                src={order.seller?.profile_picture_url ? `${import.meta.env.VITE_ASSET_HOST_URL}${order.seller.profile_picture_url}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(`${order.seller?.first_name || ''} ${order.seller?.last_name || ''}`)}`}
                alt={`${order.seller?.first_name || ''} ${order.seller?.last_name || ''}`}
              />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {order.seller?.first_name} {order.seller?.last_name}
              </p>
              <p className="text-xs text-gray-500">{order.seller?.email}</p>
            </div>
          </div>
        ),
        amount: formatCurrency(order.total_amount),
        order_status: getOrderStatusBadge(order.order_status),
        payment_status: getPaymentStatusBadge(order.payment_status),
        date: formatDateTime(order.created_at),
        action: (
          <div className="flex space-x-2">
            <button
              className="btn btn-sm bg-blue-500 hover:bg-blue-600 text-white"
              onClick={() => handleOrderClick(order.id)}
            >
              View
            </button>
          </div>
        ),
      };
    });
  }, [orders]);

  const changePage = (param) => {
    setApiParam(param);
  };

  const columns = [
    {
      label: "Order ID",
      field: "id",
    },
    {
      label: "Buyer",
      field: "buyer",
    },
    {
      label: "Seller",
      field: "seller",
    },
    {
      label: "Amount",
      field: "amount",
    },
    {
      label: "Order Status",
      field: "order_status",
    },
    {
      label: "Payment Status",
      field: "payment_status",
    },
    {
      label: "Date",
      field: "date",
    },
    {
      label: "Action",
      field: "action",
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      {isLoading || isFetching ? (
        <Loading />
      ) : isError ? (
        <p className="text-red-500">Something went wrong</p>
      ) : (
        <BasicTablePage
          title={`${statusParam ? statusParam.charAt(0).toUpperCase() + statusParam.slice(1) : 'All'} Orders`}
          columns={columns}
          data={tableData}
          changePage={changePage}
          currentPage={ordersResponse?.meta?.page || 1}
          setFilter={setApiParam}
          totalPages={ordersResponse?.meta?.last_page || 1}
          hideCreateButton={true}
        />
      )}
    </div>
  );
};

export default OrderPage;
