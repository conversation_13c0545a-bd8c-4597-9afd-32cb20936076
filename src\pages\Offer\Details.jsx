import React, { useState } from "react";
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import InputField from "@/components/ui/InputField";
import TextareaField from "@/components/ui/TextareaField";
import Card from "@/components/ui/Card";
import { DEFAULT_USER_ICON } from "@/config";
import StatusHistory from "@/components/request/StatusHistory";
import { toast } from 'react-toastify';
import * as Yup from 'yup';
import { Formik, Form, Field, ErrorMessage } from 'formik';

const OfferDetailsPage = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [showApproveModal, setShowApproveModal] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);

    const { data: responseData, isLoading, isError, refetch } = useGetApiQuery(`admin/offers/${id}`);
    const [submitMergedOffers, { isLoading: isSubmitting }] = usePostApiMutation();

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Get status color
    const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'approved':
                return 'success';
            case 'rejected':
                return 'danger';
            case 'pending':
                return 'warning';
            case 'merged':
                return 'info';
            default:
                return 'primary';
        }
    };

    // Validation schema for merged offers
    const mergedOfferSchema = Yup.object().shape({
        message: Yup.string()
            .required('Message is required')
            .max(1000, 'Message is too long (max 1000 characters)'),
        description: Yup.string()
            .required('Description is required')
            .max(5000, 'Description is too long (max 5000 characters)'),
        children: Yup.lazy(obj => 
            Yup.object().shape(
                Object.keys(obj).reduce((shape, key) => {
                    shape[key] = Yup.object().shape({
                        price: Yup.number()
                            .required('Price is required')
                            .min(0.01, 'Price must be greater than 0')
                            .typeError('Must be a valid number'),
                        delivery_time: Yup.number()
                            .required('Delivery time is required')
                            .min(1, 'Delivery time must be at least 1 day')
                            .integer('Must be a whole number')
                            .typeError('Must be a valid number')
                    });
                    return shape;
                }, {})
            )
        )
    });

    // Initial form values
    const getInitialValues = (offer) => {
        const initialValues = {
            message: '',
            description: '',
            children: {}
        };

        if (offer.request?.merged_children) {
            offer.request.merged_children.forEach(child => {
                initialValues.children[child.id] = {
                    price: '',
                    delivery_time: offer.delivery_time
                };
            });
        }

        return initialValues;
    };

    // Handle submit for merged children offers
    const handleSubmitMergedOffers = async (values) => {
        try {
            const payload = {
                message: values.message,
                description: values.description,
                merged_children_offers: Object.entries(values.children).map(([childId, data]) => ({
                    request_id: childId,
                    price: parseFloat(data.price),
                    delivery_time: parseInt(data.delivery_time)
                }))
            };

            const response = await submitMergedOffers({
                end_point: '/admin/requests/merge-with-offers',
                body: payload
            });

            if (response.error) {
                console.error('Error submitting merged offers:', response.error);
                toast.error(response.error.data?.message || 'Failed to submit merged offers');
            } else {
                toast.success('Merged offers submitted successfully!');
                refetch();
            }
        } catch (error) {
            console.error('Error submitting merged offers:', error);
            toast.error('An error occurred while submitting merged offers');
        }
    };

    if (isLoading) return <Loading />;
    if (isError) return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
            <h2 className="text-xl font-semibold text-red-700">Error Loading Offer</h2>
            <p className="mt-2 text-red-600">There was an error loading the offer details. Please try again later.</p>
            <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate(-1)}
            >
                Go Back
            </Button>
        </div>
    );

    const offer = responseData.data;

    return (
        <div className="mx-auto px-4 sm:px-6 lg:px-8 py-6 bg-gray-50 min-h-screen">
            {/* Header with breadcrumb and actions */}
            <div className="mb-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Offer Details</h1>
                        <div className="flex items-center mt-1">
                            <Badge color={getStatusColor(offer.status)} className="mr-2">
                                {offer.status}
                            </Badge>
                            <span className="text-sm text-gray-500">
                                Created {formatDate(offer.created_at)}
                            </span>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Button
                            variant="outline"
                            onClick={() => navigate('/offers')}
                            className="border-gray-300 hover:bg-gray-50"
                            icon="heroicons:arrow-left"
                        >
                            Back to Offers
                        </Button>
                        {offer.status !== 'Approved' && (
                            <Button
                                variant="success"
                                onClick={() => setShowApproveModal(true)}
                                icon="heroicons:check"
                            >
                                Approve
                            </Button>
                        )}
                        {offer.status !== 'Rejected' && (
                            <Button
                                variant="danger"
                                onClick={() => setShowRejectModal(true)}
                                icon="heroicons:x-mark"
                            >
                                Reject
                            </Button>
                        )}
                    </div>
                </div>
            </div>

            {/* Main content */}
            <div className="grid grid-cols-12 gap-6">
                {/* Left column - Offer details */}
                <div className="col-span-12 lg:col-span-8 space-y-6">
                    {/* Offer summary card */}
                    <Card>
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Offer Summary</h3>
                        </div>
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons:currency-dollar" className="h-5 w-5 text-primary-500 mr-2" />
                                        <span className="text-sm font-medium text-gray-500">Price</span>
                                    </div>
                                    <p className="text-xl font-semibold text-gray-900">{formatCurrency(offer.price)}</p>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons:clock" className="h-5 w-5 text-primary-500 mr-2" />
                                        <span className="text-sm font-medium text-gray-500">Delivery Time</span>
                                    </div>
                                    <p className="text-xl font-semibold text-gray-900">{offer.delivery_time} days</p>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons:calendar" className="h-5 w-5 text-primary-500 mr-2" />
                                        <span className="text-sm font-medium text-gray-500">Last Updated</span>
                                    </div>
                                    <p className="text-sm font-medium text-gray-900">{formatDate(offer.updated_at)}</p>
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* Offer message card */}
                    <Card>
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Offer Message</h3>
                        </div>
                        <div className="p-6">
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                                <p className="text-gray-700 whitespace-pre-wrap">{offer.message || 'No message provided.'}</p>
                            </div>
                        </div>
                    </Card>

                    {/* Offer description card */}
                    <Card>
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Offer Description</h3>
                        </div>
                        <div className="p-6">
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                                <p className="text-gray-700 whitespace-pre-wrap">{offer.description || 'No description provided.'}</p>
                            </div>
                        </div>
                    </Card>

                    {/* Merged Children Requests */}
                    {offer.request?.merged_children && offer.request.merged_children.length > 0 && (
                        <Formik
                            initialValues={getInitialValues(offer)}
                            validationSchema={mergedOfferSchema}
                            onSubmit={handleSubmitMergedOffers}
                        >
                            {({ values, errors, touched, isSubmitting }) => (
                                <Form>
                                    <Card>
                                        <div className="px-6 py-4 border-b border-gray-100">
                                            <h3 className="text-lg font-semibold text-gray-900">
                                                Merged Requests ({offer.request.merged_children.length})
                                            </h3>
                                            <p className="text-sm text-gray-600 mt-1">
                                                Set individual pricing and delivery time for each merged request
                                            </p>
                                        </div>
                                        <div className="p-6 space-y-6">
                                            {offer.request.merged_children.map((child) => (
                                                <Card key={child.id} className="border border-gray-200">
                                                    <div className="p-4">
                                                        <div className="flex items-start justify-between mb-4">
                                                            <div className="flex-1">
                                                                <h4 className="font-semibold text-gray-900 mb-2">{child.title}</h4>
                                                                <p className="text-sm text-gray-600 mb-3">{child.description}</p>

                                                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Quantity:</span>
                                                                        <p className="text-gray-900">{child.quantity}</p>
                                                                    </div>
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Budget:</span>
                                                                        <p className="text-gray-900">
                                                                            {formatCurrency(child.budget_min)} - {formatCurrency(child.budget_max)}
                                                                        </p>
                                                                    </div>
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Deadline:</span>
                                                                        <p className="text-gray-900">{formatDate(child.deadline)}</p>
                                                                    </div>
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Urgency:</span>
                                                                        <Badge color={child.urgency === 'High' ? 'danger' : child.urgency === 'Normal' ? 'primary' : 'success'}>
                                                                            {child.urgency}
                                                                        </Badge>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Price and Delivery Time Inputs */}
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-200">
                                                            <div>
                                                                <label htmlFor={`children.${child.id}.price`} className="block text-sm font-medium text-gray-700 mb-2">
                                                                    Your Price ($)
                                                                </label>
                                                                <Field
                                                                    type="number"
                                                                    min="0"
                                                                    step="0.01"
                                                                    placeholder="Enter your price"
                                                                    name={`children.${child.id}.price`}
                                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                                                />
                                                                <ErrorMessage name={`children.${child.id}.price`} component="div" className="text-sm text-red-600 mt-1" />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`children.${child.id}.delivery_time`} className="block text-sm font-medium text-gray-700 mb-2">
                                                                    Delivery Time (days)
                                                                </label>
                                                                <Field
                                                                    type="number"
                                                                    min="1"
                                                                    placeholder="Enter delivery days"
                                                                    name={`children.${child.id}.delivery_time`}
                                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                                                />
                                                                <ErrorMessage name={`children.${child.id}.delivery_time`} component="div" className="text-sm text-red-600 mt-1" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Card>
                                            ))}

                                            {/* Message and Description for the overall offer */}
                                            <div className="mt-6 pt-6 border-t border-gray-200">
                                                <h4 className="font-semibold text-gray-900 mb-4">Overall Offer Details</h4>
                                                <div className="space-y-4">
                                                    <div>
                                                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                                                            Message
                                                        </label>
                                                        <Field
                                                            as="textarea"
                                                            rows={3}
                                                            placeholder="Enter your message for this offer"
                                                            name="message"
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                                        />
                                                        <ErrorMessage name="message" component="div" className="text-sm text-red-600 mt-1" />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                                                            Description
                                                        </label>
                                                        <Field
                                                            as="textarea"
                                                            rows={4}
                                                            placeholder="Enter detailed description of your offer"
                                                            name="description"
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                                        />
                                                        <ErrorMessage name="description" component="div" className="text-sm text-red-600 mt-1" />
                                                    </div>
                                                </div>

                                                {/* Submit Button */}
                                                <div className="mt-6">
                                                    <Button
                                                        type="submit"
                                                        variant="primary"
                                                        disabled={isSubmitting}
                                                        isLoading={isSubmitting}
                                                        className="w-full md:w-auto"
                                                        icon="heroicons:paper-airplane"
                                                    >
                                                        {isSubmitting ? 'Submitting...' : 'Submit Merged Offer'}
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </Card>
                                </Form>
                            )}
                        </Formik>
                    )}
                </div>

                {/* Right column - Related information */}
                <div className="col-span-12 lg:col-span-4 space-y-6">
                    {/* Seller card */}
                    <Card>
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Seller Information</h3>
                        </div>
                        <div className="p-6">
                            <div className="flex items-center mb-4">
                                <div className="h-16 w-16 rounded-full overflow-hidden bg-gray-100 mr-4 border border-gray-200">
                                    <img
                                        src={offer.seller?.profile_picture_url
                                            ? `${import.meta.env.VITE_ASSET_HOST_URL}${offer.seller.profile_picture_url}`
                                            : DEFAULT_USER_ICON
                                        }
                                        alt={`${offer.seller?.first_name || 'Seller'} ${offer.seller?.last_name || ''}`}
                                        className="h-full w-full object-cover"
                                        onError={(e) => {
                                            e.target.src = DEFAULT_USER_ICON;
                                        }}
                                    />
                                </div>
                                <div>
                                    <Link
                                        to={`/user/${offer.seller?.id}`}
                                        className="text-lg font-semibold text-primary-600 hover:text-primary-700"
                                    >
                                        {`${offer.seller?.first_name || ''} ${offer.seller?.last_name || ''}`}
                                    </Link>
                                    <p className="text-sm text-gray-500">{offer.seller?.email || 'No email available'}</p>
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* Request card */}
                    <Card>
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Request Information</h3>
                        </div>
                        <div className="p-6">
                            <Link
                                to={`/request/${offer.request?.id}`}
                                className="text-lg font-semibold text-primary-600 hover:text-primary-700 block mb-4"
                            >
                                {offer.request?.title || 'N/A'}
                            </Link>

                            <div className="space-y-3">
                                <div className="flex items-center">
                                    <Icon icon="heroicons:tag" className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-500 mr-1">Category:</span>
                                    <span className="text-sm font-medium text-gray-700">{offer.request?.category?.title || 'N/A'}</span>
                                </div>

                                <div className="flex items-center">
                                    <Icon icon="heroicons:tag" className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-500 mr-1">Subcategory:</span>
                                    <span className="text-sm font-medium text-gray-700">{offer.request?.sub_category?.title || 'N/A'}</span>
                                </div>

                                <div className="flex items-center">
                                    <Icon icon="heroicons:currency-dollar" className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-500 mr-1">Budget:</span>
                                    <span className="text-sm font-medium text-gray-700">
                                        {formatCurrency(offer.request?.budget_min)} - {formatCurrency(offer.request?.budget_max)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* Status History */}
                    {offer.offer_status_changes && offer.offer_status_changes.length > 0 && (
                        <StatusHistory statusHistory={offer.offer_status_changes} />
                    )}
                </div>
            </div>

            {/* Modals */}
            {showApproveModal && (
                <ApproveModal
                    showApproveModal={showApproveModal}
                    setShowApproveModal={setShowApproveModal}
                    data={offer}
                    onSuccess={() => refetch()}
                />
            )}
            {showRejectModal && (
                <RejectModal
                    showRejectModal={showRejectModal}
                    setShowRejectModal={setShowRejectModal}
                    data={offer}
                    onSuccess={() => refetch()}
                />
            )}
        </div>
    );
};

export default OfferDetailsPage;