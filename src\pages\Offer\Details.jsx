import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import ApproveModal from "./ApproveModal";
import RejectModal from "./RejectModal";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { DEFAULT_USER_ICON } from "@/config";
import StatusHistory from "@/components/request/StatusHistory";

const OfferDetailsPage = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [showApproveModal, setShowApproveModal] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);

    const { data: responseData, isLoading, isError, refetch } = useGetApiQuery(`admin/offers/${id}`);

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Get status color
    const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'approved':
                return 'success';
            case 'rejected':
                return 'danger';
            case 'pending':
                return 'warning';
            case 'merged':
                return 'info';
            default:
                return 'primary';
        }
    };

    if (isLoading) return <Loading />;
    if (isError) return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
            <h2 className="text-xl font-semibold text-red-700">Error Loading Offer</h2>
            <p className="mt-2 text-red-600">There was an error loading the offer details. Please try again later.</p>
            <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate(-1)}
            >
                Go Back
            </Button>
        </div>
    );

    const offer = responseData.data;

    return (
        <div className="mx-auto px-6 py-6 bg-gray-50 min-h-screen">
            {/* Header with breadcrumb and actions */}
            <div className="mb-6">
      
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Offer Details</h1>
                        <div className="flex items-center mt-1">
                            <Badge color={getStatusColor(offer.status)} className="mr-2">
                                {offer.status}
                            </Badge>
                            <span className="text-sm text-gray-500">
                                Created {formatDate(offer.created_at)}
                            </span>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Button
                            variant="outline"
                            onClick={() => navigate('/offers')}
                            className="border-gray-300 hover:bg-gray-50"
                            icon="heroicons:arrow-left"
                        >
                            Back to Offers
                        </Button>
                        {offer.status !== 'Approved' && (
                            <Button
                                variant="success"
                                onClick={() => setShowApproveModal(true)}
                                icon="heroicons:check"
                            >
                                Approve
                            </Button>
                        )}
                        {offer.status !== 'Rejected' && (
                            <Button
                                variant="danger"
                                onClick={() => setShowRejectModal(true)}
                                icon="heroicons:x-mark"
                            >
                                Reject
                            </Button>
                        )}
                    </div>
                </div>
            </div>

            {/* Main content */}
            <div className="grid grid-cols-12 gap-6">
                {/* Left column - Offer details */}
                <div className="col-span-12 lg:col-span-8 space-y-6">
                    {/* Offer summary card */}
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Offer Summary</h3>
                        </div>
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons:currency-dollar" className="h-5 w-5 text-primary-500 mr-2" />
                                        <span className="text-sm font-medium text-gray-500">Price</span>
                                    </div>
                                    <p className="text-xl font-semibold text-gray-900">{formatCurrency(offer.price)}</p>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons:clock" className="h-5 w-5 text-primary-500 mr-2" />
                                        <span className="text-sm font-medium text-gray-500">Delivery Time</span>
                                    </div>
                                    <p className="text-xl font-semibold text-gray-900">{offer.delivery_time} days</p>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                    <div className="flex items-center mb-2">
                                        <Icon icon="heroicons:calendar" className="h-5 w-5 text-primary-500 mr-2" />
                                        <span className="text-sm font-medium text-gray-500">Last Updated</span>
                                    </div>
                                    <p className="text-sm font-medium text-gray-900">{formatDate(offer.updated_at)}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Offer message card */}
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Offer Message</h3>
                        </div>
                        <div className="p-6">
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                                <p className="text-gray-700 whitespace-pre-wrap">{offer.message || 'No message provided.'}</p>
                            </div>
                        </div>
                    </div>

                    {/* Offer description card */}
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Offer Description</h3>
                        </div>
                        <div className="p-6">
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                                <p className="text-gray-700 whitespace-pre-wrap">{offer.description || 'No description provided.'}</p>
                            </div>
                        </div>
                    </div>

                    {/* No status history here - moved to right column */}
                </div>

                {/* Right column - Related information */}
                <div className="col-span-12 lg:col-span-4 space-y-6">

                    {/* Seller card */}
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Seller Information</h3>
                        </div>
                        <div className="p-6">
                            <div className="flex items-center mb-4">
                                <div className="h-16 w-16 rounded-full overflow-hidden bg-gray-100 mr-4 border border-gray-200">
                                    <img
                                        src={offer.seller?.profile_picture_url
                                            ? `${import.meta.env.VITE_ASSET_HOST_URL}${offer.seller.profile_picture_url}`
                                            : DEFAULT_USER_ICON
                                        }
                                        alt={`${offer.seller?.first_name || 'Seller'} ${offer.seller?.last_name || ''}`}
                                        className="h-full w-full object-cover"
                                        onError={(e) => {
                                            e.target.src = DEFAULT_USER_ICON;
                                        }}
                                    />
                                </div>
                                <div>
                                    <Link
                                        to={`/user/${offer.seller?.id}`}
                                        className="text-lg font-semibold text-primary-600 hover:text-primary-700"
                                    >
                                        {`${offer.seller?.first_name || ''} ${offer.seller?.last_name || ''}`}
                                    </Link>
                                    <p className="text-sm text-gray-500">{offer.seller?.email || 'No email available'}</p>
                                </div>
                            </div>


                        </div>
                    </div>

                    {/* Request card */}
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">Request Information</h3>
                        </div>
                        <div className="p-6">
                            <Link
                                to={`/request/${offer.request?.id}`}
                                className="text-lg font-semibold text-primary-600 hover:text-primary-700 block mb-4"
                            >
                                {offer.request?.title || 'N/A'}
                            </Link>

                            <div className="space-y-3">
                                <div className="flex items-center">
                                    <Icon icon="heroicons:tag" className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-500 mr-1">Category:</span>
                                    <span className="text-sm font-medium text-gray-700">{offer.request?.category?.title || 'N/A'}</span>
                                </div>

                                <div className="flex items-center">
                                    <Icon icon="heroicons:tag" className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-500 mr-1">Subcategory:</span>
                                    <span className="text-sm font-medium text-gray-700">{offer.request?.sub_category?.title || 'N/A'}</span>
                                </div>

                                <div className="flex items-center">
                                    <Icon icon="heroicons:currency-dollar" className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-500 mr-1">Budget:</span>
                                    <span className="text-sm font-medium text-gray-700">
                                        {formatCurrency(offer.request?.budget_min)} - {formatCurrency(offer.request?.budget_max)}
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
                       {/* Status History */}
                       {offer.offer_status_changes && offer.offer_status_changes.length > 0 && (
                        <div>
                            <StatusHistory statusHistory={offer.offer_status_changes} />
                        </div>
                    )}

                </div>
            </div>

            {/* Modals */}
            {showApproveModal && (
                <ApproveModal
                    showApproveModal={showApproveModal}
                    setShowApproveModal={setShowApproveModal}
                    data={offer}
                    onSuccess={() => refetch()}
                />
            )}
            {showRejectModal && (
                <RejectModal
                    showRejectModal={showRejectModal}
                    setShowRejectModal={setShowRejectModal}
                    data={offer}
                    onSuccess={() => refetch()}
                />
            )}
        </div>
    );
};

export default OfferDetailsPage;
