import React, { useState, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";

const BulkUploadSubcategories = () => {
  const { categoryId } = useParams();
  const navigate = useNavigate();
  const [postApi, { isLoading }] = usePostApiMutation();
  const token = useSelector((state) => state.auth?.token);

  const [uploadStep, setUploadStep] = useState('upload'); // 'upload', 'preview', 'processing'
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewData, setPreviewData] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        toast.error("Please select a valid CSV file");
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error("File size must be less than 10MB");
        return;
      }

      setSelectedFile(file);
    }
  };

  const downloadTemplate = async (format) => {
    try {
      // Create a direct download link for the template
      const downloadUrl = `${import.meta.env.VITE_HOST_URL}admin/bulk-upload/template/${format}`;
      
      // Get the auth token from Redux store or cookies as fallback
      const authToken = token || document.cookie
        .split('; ')
        .find(cookie => cookie.startsWith('accessToken='))
        ?.split('=')[1];
      
      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'application/octet-stream',
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `subcategories_template.${format === 'excel' ? 'xlsx' : 'csv'}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        
        toast.success(`Template downloaded successfully`);
      } else {
        toast.error("Failed to download template");
      }
    } catch (error) {
      toast.error("An error occurred while downloading template");
      console.error(error);
    }
  };

  const handlePreview = async () => {
    if (!selectedFile) {
      toast.error("Please select a file first");
      return;
    }

    try {
      setUploadStep('preview');
      
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('category_id', categoryId);

      const response = await postApi({
        end_point: '/admin/bulk-upload/preview',
        body: formData
      });

      if (!response.error) {
        console.log(response.data);
        setPreviewData(response?.data?.data);
      } else {
        toast.error(response.error?.data?.message || "Failed to preview file");
        setUploadStep('upload');
      }
    } catch (error) {
      toast.error("An error occurred while previewing file");
      setUploadStep('upload');
      console.error(error);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !previewData) {
      toast.error("No file or preview data available");
      return;
    }

    try {
      setUploadStep('processing');
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('category_id', categoryId);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const response = await postApi({
        end_point: '/admin/bulk-upload/subcategories',
        body: formData
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.error) {
        // toast.success(`Successfully uploaded ${response.data.success_count} subcategories`);
        if (response.data.error_count > 0) {
          toast.warning(`${response.data.error_count} rows had errors and were skipped`);
        }
        
        setTimeout(() => {
          navigate(`/category/${categoryId}`);
        }, 1500);
      } else {
        toast.error(response.error?.data?.message || "Failed to upload subcategories");
        setUploadStep('preview');
      }
    } catch (error) {
      toast.error("An error occurred while uploading subcategories");
      setUploadStep('preview');
      console.error(error);
    }
  };



  const handleBack = () => {
    if (uploadStep === 'upload') {
      navigate(`/category/${categoryId}`);
    } else {
      setUploadStep('upload');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            icon="heroicons:chevron-left"
            onClick={handleBack}
            variant="outline"
            className="h-10 w-10 p-0"
          />
          <div>
            <h1 className="text-lg font-medium text-gray-900 dark:text-white">
              Bulk Upload Subcategories
            </h1>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Upload multiple subcategories using CSV file
            </p>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center gap-2">
          <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm ${
            uploadStep === 'upload' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
          }`}>
            <div className={`w-2 h-2 rounded-full ${uploadStep === 'upload' ? 'bg-blue-600' : 'bg-gray-400'}`} />
            Upload
          </div>
          <Icon icon="heroicons:chevron-right" className="w-4 h-4 text-gray-400" />
          <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm ${
            uploadStep === 'preview' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
          }`}>
            <div className={`w-2 h-2 rounded-full ${uploadStep === 'preview' ? 'bg-blue-600' : 'bg-gray-400'}`} />
            Preview
          </div>
          <Icon icon="heroicons:chevron-right" className="w-4 h-4 text-gray-400" />
          <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm ${
            uploadStep === 'processing' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
          }`}>
            <div className={`w-2 h-2 rounded-full ${uploadStep === 'processing' ? 'bg-blue-600' : 'bg-gray-400'}`} />
            Process
          </div>
        </div>
      </div>

      {/* Content based on current step */}
      {uploadStep === 'upload' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Template Download Section */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Icon icon="heroicons:document-arrow-down" className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white text-sm">Step 1: Download Template</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Get the correct format</p>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Download the template file with the correct format and sample data to ensure your upload is successful.
              </p>
              
              <div className="space-y-3">
                <Button
                  onClick={() => downloadTemplate('csv')}
                  className="w-full justify-center bg-green-600 hover:bg-green-700 text-white border-0"
                  icon="heroicons:arrow-down-tray"
                >
                  <Icon icon="heroicons-solid:download" className="w-5 h-5 text-white mt-0.5 mr-2" />
                  Download Sample CSV 
                </Button>
           
              </div>

              <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="flex items-start gap-3">
                  <Icon icon="heroicons:light-bulb" className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1 text-xs">Template Guidelines</h4>
                    <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
                      <li>• Fill in all required fields (title, description)</li>
                      <li>• Use the exact column headers provided</li>
                      <li>• Keep file size under 10MB</li>
                      <li>• Remove sample data before uploading</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* File Upload Section */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Icon icon="heroicons:cloud-arrow-up" className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white text-sm">Step 2: Upload Your File</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Select your completed file</p>
                </div>
              </div>
              
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                
                {selectedFile ? (
                  <div className="space-y-4">
                    <Icon icon="heroicons:document-check" className="w-16 h-16 text-green-500 mx-auto" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white text-sm">{selectedFile.name}</p>
                      <p className="text-xs text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <div className="flex gap-3 justify-center">
                      <Button
                        onClick={() => fileInputRef.current?.click()}
                        variant="outline"
                        size="sm"
                      >
                        Change File
                      </Button>
                      <Button
                        onClick={handlePreview}
                        icon="heroicons:eye"
                        isLoading={isLoading}
                      >
                        Preview Data
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Icon icon="heroicons:cloud-arrow-up" className="w-16 h-16 text-gray-400 mx-auto" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white text-sm">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">CSV files only (max 10MB)</p>
                    </div>
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      variant="outline"
                    >
                      Select File
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      )}

      {uploadStep === 'preview' && previewData && (
        <div className="space-y-6">
          {/* File Information */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Icon icon="heroicons:document-text" className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white text-sm">File Information</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Details about your uploaded file</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Filename</div>
                  <div className="font-medium text-gray-900 dark:text-white">{previewData.file_info?.filename}</div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div className="text-sm text-gray-500 dark:text-gray-400">File Size</div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {previewData.file_info?.size ? `${(previewData.file_info.size / 1024).toFixed(2)} KB` : 'N/A'}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div className="text-sm text-gray-500 dark:text-gray-400">File Type</div>
                  <div className="font-medium text-gray-900 dark:text-white uppercase">{previewData.file_info?.type}</div>
                </div>
              </div>
            </div>
          </Card>

          {/* Validation Summary */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className={`flex items-center justify-center w-10 h-10 rounded-lg ${
                  previewData.validation_summary?.is_valid
                    ? 'bg-green-100 dark:bg-green-900/20'
                    : 'bg-red-100 dark:bg-red-900/20'
                }`}>
                  <Icon
                    icon={previewData.validation_summary?.is_valid ? "heroicons:check-circle" : "heroicons:exclamation-triangle"}
                    className={`w-5 h-5 ${
                      previewData.validation_summary?.is_valid
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}
                  />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white text-sm">Validation Summary</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {previewData.validation_summary?.is_valid ? 'All data is valid and ready for upload' : 'Some data needs attention'}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {previewData.validation_summary?.total_rows || 0}
                  </div>
                  <div className="text-sm text-blue-600 dark:text-blue-400">Total Rows</div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {previewData.validation_summary?.valid_rows || 0}
                  </div>
                  <div className="text-sm text-green-600 dark:text-green-400">Valid Rows</div>
                </div>
                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {previewData.validation_summary?.invalid_rows || 0}
                  </div>
                  <div className="text-sm text-red-600 dark:text-red-400">Invalid Rows</div>
                </div>
              </div>
            </div>
          </Card>

          {/* Creation Summary */}
          {previewData.creation_summary && (
            <Card>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                    <Icon icon="heroicons:plus-circle" className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white text-sm">What Will Be Created</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Summary of items that will be added to category: <span className="font-medium">{previewData.creation_summary.category_info?.title}</span>
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {previewData.creation_summary.subcategories_to_create || 0}
                    </div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">Subcategories</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">New subcategories to be created</div>
                  </div>
                  <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4">
                    <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                      {previewData.creation_summary.form_fields_to_create || 0}
                    </div>
                    <div className="text-sm text-indigo-600 dark:text-indigo-400">Form Fields</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Custom fields for data collection</div>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Validation Errors */}
          {previewData?.errors?.length > 0 && (
            <Card>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Icon icon="heroicons:exclamation-triangle" className="w-5 h-5 text-red-600" />
                  <h3 className="font-medium text-red-800 dark:text-red-200 text-sm">Validation Errors</h3>
                  <Badge className="bg-red-100 text-red-800">{previewData.errors.length} errors</Badge>
                </div>
                <div className="max-h-48 overflow-y-auto bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                  <ul className="text-sm text-red-700 dark:text-red-300 space-y-2">
                    {previewData.errors.map((error, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="font-medium">Row {error.row}:</span>
                        <span>{error.message}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </Card>
          )}

          {/* Action Buttons */}
          <Card>
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1 text-sm">Ready to Upload?</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {previewData.validation_summary?.is_valid
                      ? 'All data is valid. You can proceed with the upload.'
                      : 'Please fix the validation errors before uploading.'
                    }
                  </p>
                </div>
                <div className="flex gap-3">
                  <Button
                    onClick={() => setUploadStep('upload')}
                    variant="outline"
                  >
                    Back to Upload
                  </Button>
                  <Button
                    onClick={handleUpload}
                    disabled={!previewData.validation_summary?.is_valid}
                    icon="heroicons:cloud-arrow-up"
                    isLoading={isLoading}
                  >
                    Upload Data
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          {/* Preview Data Table */}
          {previewData?.preview_data?.length > 0 && (
            <Card>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg">
                    <Icon icon="heroicons:table-cells" className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white text-sm">Sample Data Preview</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">First 5 rows from your file</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {previewData.preview_data.slice(0, 5).map((row, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900 dark:text-white text-sm">Row {index + 1}</h4>
                        <Badge className="bg-gray-100 text-gray-800 text-xs">
                          {row.subcategory_title ? 'Subcategory' : 'Form Field'}
                        </Badge>
                      </div>

                      {row.subcategory_title && (
                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 mb-3">
                          <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2 text-xs">Subcategory Information</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-blue-700 dark:text-blue-300 font-medium">Title:</span>
                              <span className="ml-2 text-blue-900 dark:text-blue-100">{row.subcategory_title}</span>
                            </div>
                            <div>
                              <span className="text-blue-700 dark:text-blue-300 font-medium">Featured:</span>
                              <span className="ml-2 text-blue-900 dark:text-blue-100">{row.is_featured || 'false'}</span>
                            </div>
                            <div className="md:col-span-2">
                              <span className="text-blue-700 dark:text-blue-300 font-medium">Description:</span>
                              <span className="ml-2 text-blue-900 dark:text-blue-100">{row.subcategory_description}</span>
                            </div>
                            {row.subcategory_color && (
                              <div>
                                <span className="text-blue-700 dark:text-blue-300 font-medium">Color:</span>
                                <span className="ml-2 text-blue-900 dark:text-blue-100">{row.subcategory_color}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {row.label_name && (
                        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                          <h5 className="font-medium text-purple-900 dark:text-purple-100 mb-2 text-xs">Form Field Information</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-purple-700 dark:text-purple-300 font-medium">Label:</span>
                              <span className="ml-2 text-purple-900 dark:text-purple-100">{row.label_name}</span>
                            </div>
                            <div>
                              <span className="text-purple-700 dark:text-purple-300 font-medium">Type:</span>
                              <span className="ml-2 text-purple-900 dark:text-purple-100">{row.input_type}</span>
                            </div>
                            {row.label_subtitle && (
                              <div>
                                <span className="text-purple-700 dark:text-purple-300 font-medium">Subtitle:</span>
                                <span className="ml-2 text-purple-900 dark:text-purple-100">{row.label_subtitle}</span>
                              </div>
                            )}
                            {row.placeholder && (
                              <div>
                                <span className="text-purple-700 dark:text-purple-300 font-medium">Placeholder:</span>
                                <span className="ml-2 text-purple-900 dark:text-purple-100">{row.placeholder}</span>
                              </div>
                            )}
                            <div>
                              <span className="text-purple-700 dark:text-purple-300 font-medium">Required:</span>
                              <span className="ml-2 text-purple-900 dark:text-purple-100">{row.is_required || 'false'}</span>
                            </div>
                            {row.options && (
                              <div>
                                <span className="text-purple-700 dark:text-purple-300 font-medium">Options:</span>
                                <span className="ml-2 text-purple-900 dark:text-purple-100">{row.options}</span>
                              </div>
                            )}
                            {row.default_value && (
                              <div>
                                <span className="text-purple-700 dark:text-purple-300 font-medium">Default:</span>
                                <span className="ml-2 text-purple-900 dark:text-purple-100">{row.default_value}</span>
                              </div>
                            )}
                            {(row.min_value || row.max_value) && (
                              <div>
                                <span className="text-purple-700 dark:text-purple-300 font-medium">Range:</span>
                                <span className="ml-2 text-purple-900 dark:text-purple-100">
                                  {row.min_value && `Min: ${row.min_value}`}
                                  {row.min_value && row.max_value && ', '}
                                  {row.max_value && `Max: ${row.max_value}`}
                                </span>
                              </div>
                            )}
                            {(row.min_length || row.max_length) && (
                              <div>
                                <span className="text-purple-700 dark:text-purple-300 font-medium">Length:</span>
                                <span className="ml-2 text-purple-900 dark:text-purple-100">
                                  {row.min_length && `Min: ${row.min_length}`}
                                  {row.min_length && row.max_length && ', '}
                                  {row.max_length && `Max: ${row.max_length}`}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {previewData.preview_data.length > 5 && (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                      ... and {previewData.preview_data.length - 5} more rows
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )}
        </div>
      )}

      {uploadStep === 'processing' && (
        <Card>
          <div className="p-12 text-center">
            <Icon icon="heroicons:arrow-path" className="w-16 h-16 text-blue-500 mx-auto animate-spin mb-6" />
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Processing Upload...
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-6">
              Please wait while we process your subcategories. This may take a few moments.
            </p>
            
            <div className="max-w-md mx-auto">
              <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">{uploadProgress}% complete</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default BulkUploadSubcategories;
