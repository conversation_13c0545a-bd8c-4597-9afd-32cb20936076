import { setToken } from "@/store/api/auth/authSlice";
import { store } from "@/store";

/**
 * Attempts to refresh the access token using the refresh token
 * @returns {Promise<string|null>} The new access token or null if refresh failed
 */
export const refreshAccessToken = async () => {
  try {
    // Get the refresh token from cookies
    const refreshToken = document.cookie
      .split('; ')
      .find(cookie => cookie.startsWith('refreshToken='))
      ?.split('=')[1];

    if (!refreshToken) {
      console.error('No refresh token available');
      return null;
    }

    // Call the token refresh endpoint
    const response = await fetch(`${import.meta.env.VITE_HOST_URL}/admin/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    
    if (data.accessToken) {
      // Update the token in Redux store
      store.dispatch(setToken(data.accessToken));
      
      // Update the token in cookies
      document.cookie = `accessToken=${data.accessToken}; path=/; SameSite=Strict`;
      
      // If a new refresh token is provided, update it too
      if (data.refreshToken) {
        document.cookie = `refreshToken=${data.refreshToken}; path=/; SameSite=Strict`;
      }
      
      return data.accessToken;
    }
    
    return null;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
};
