import React from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import Icon from "@/components/ui/Icon";
import Card from "@/components/ui/Card";

const BlogPostDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const { 
    data: responseData, 
    isLoading, 
    isError 
  } = useGetApiQuery(`/admin/blog/posts/${id}`);

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'published': return 'bg-green-500 text-white';
      case 'draft': return 'bg-gray-500 text-white';
      case 'scheduled': return 'bg-blue-500 text-white';
      case 'archived': return 'bg-red-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (isError || !responseData?.data) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Icon icon="heroicons:exclamation-triangle" className="w-16 h-16 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Blog Post Not Found</h2>
        <p className="text-gray-600 mb-4">The blog post you're looking for doesn't exist.</p>
        <Button
          variant="primary"
          onClick={() => navigate('/blog/posts')}
        >
          Back to Blog Posts
        </Button>
      </div>
    );
  }

  const post = responseData.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Blog Post Details</h1>
          <p className="text-sm text-gray-600 mt-1">
            View and manage blog post information
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/blog/posts')}
            className="h-9 px-4 flex items-center"
          >
            <Icon icon="heroicons:arrow-left" className="mr-2 w-4 h-4" /> Back to Posts
          </Button>
          <Button
            type="button"
            variant="primary"
            onClick={() => navigate(`/blog/post/edit/${post.id}`)}
            className="h-9 px-4 flex items-center"
          >
            <Icon icon="heroicons:pencil-square" className="mr-2 w-4 h-4" /> Edit Post
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Post Content */}
          <Card>
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{post.title}</h2>
                  {post.excerpt && (
                    <p className="text-lg text-gray-600 mb-4">{post.excerpt}</p>
                  )}
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Badge className={getStatusColor(post.status)}>
                    {post.status}
                  </Badge>
                  {post.is_featured && (
                    <Badge className="bg-yellow-500 text-white">
                      Featured
                    </Badge>
                  )}
                </div>
              </div>

              {/* Featured Image */}
              {post.featured_image && (
                <div className="mb-6">
                  <img
                    src={post.featured_image}
                    alt={post.title}
                    className="w-full h-64 object-cover rounded-lg"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
              )}

              {/* Content */}
              <div className="prose max-w-none">
                <div 
                  className="text-gray-700 leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />
              </div>

              {/* Tags */}
              {post.tags && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.split(',').map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag.trim()}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* SEO Information */}
          {(post.meta_title || post.meta_description || post.meta_keywords) && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">SEO Information</h3>
                <div className="space-y-4">
                  {post.meta_title && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Meta Title</label>
                      <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                        {post.meta_title}
                      </p>
                    </div>
                  )}
                  {post.meta_description && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Meta Description</label>
                      <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                        {post.meta_description}
                      </p>
                    </div>
                  )}
                  {post.meta_keywords && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Meta Keywords</label>
                      <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                        {post.meta_keywords}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Post Information */}
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Post Information</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Author</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {post.author ? `${post.author.first_name} ${post.author.last_name}` : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Category</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {post.category?.title || 'Uncategorized'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Reading Time</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {post.reading_time ? `${post.reading_time} minutes` : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Created</label>
                  <p className="text-sm text-gray-900 mt-1">{formatDate(post.created_at)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Last Updated</label>
                  <p className="text-sm text-gray-900 mt-1">{formatDate(post.updated_at)}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Attachments */}
          {post.attachments && post.attachments.length > 0 && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Attachments</h3>
                <div className="space-y-2">
                  {post.attachments.map((attachment, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <Icon icon="heroicons:document" className="w-4 h-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-900">{attachment.file_name}</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {(attachment.file_size / 1024).toFixed(1)} KB
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogPostDetails;
