import React, { useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Button from "@/components/ui/Button";
import Loading from "@/components/Loading";
import Delete from "./Delete";
import Icon from "@/components/ui/Icon";
import CreateSubCategoryPage from "./CreateOrUpdate";
import AddFormFieldModal from "./AddFormFieldModal";
import EditFormFieldModal from "./EditFormFieldModal";
import DeleteFormFieldModal from "./DeleteFormFieldModal";
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import SeoModal from "../SeoModal";


const SubCategoryDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAddFormFieldModal, setShowAddFormFieldModal] = useState(false);
  const [showEditFormFieldModal, setShowEditFormFieldModal] = useState(false);
  const [showDeleteFormFieldModal, setShowDeleteFormFieldModal] = useState(false);
  const [selectedFormField, setSelectedFormField] = useState(null);
  const [seoModalOpen, setSeoModalOpen] = useState(false);
  const [imageZoomOpen, setImageZoomOpen] = useState(false);

  const { data: subcategoryData, isLoading, isError, refetch } = useGetApiQuery(
    `admin/subcategories/${id}`
  );

  if (isLoading) return <Loading />;
  if (isError) return <div className="text-red-500">Error fetching subcategory data</div>;
  if (!subcategoryData?.data) return <div>Subcategory not found</div>;

  const subcategory = subcategoryData.data;

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  const handleBackToCategory = () => {
    if (subcategory.category_id) {
      navigate(`/category/${subcategory.category_id}`);
    } else {
      navigate('/categories');
    }
  };

  const handleAddFormField = () => {
    setShowAddFormFieldModal(true);
  };

  const handleFormFieldAdded = () => {
    refetch(); // Refresh the data to show the new form field
  };

  const handleEditFormField = (formField) => {
    setSelectedFormField(formField);
    setShowEditFormFieldModal(true);
  };

  const handleDeleteFormField = (field) => {
    setSelectedFormField(field);
    setShowDeleteFormFieldModal(true);
  };

  // Helper function to render form field type in a readable format
  const formatFieldType = (type) => {
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };



  if (isEditing) {
    return (
      <CreateSubCategoryPage
        setIsCreatingSubCategory={setIsEditing}
        subcategroyid={id}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button
            icon="heroicons:chevron-left"
            onClick={handleBackToCategory}
            variant="outline"
            className="h-10 w-10 p-0"
          />
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {subcategory.title}
              </h1>
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                Subcategory Details & Form Field Management
              </p>
            </div>
            {subcategory.image && (
              <div
                className="relative h-16 w-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-lg transition-shadow duration-200"
                onClick={() => setImageZoomOpen(true)}
                title="Click to zoom"
              >
                <img
                  src={import.meta.env.VITE_ASSET_HOST_URL + subcategory.image}
                  alt={subcategory.title}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            icon="heroicons:cog-6-tooth"
            onClick={() => setSeoModalOpen(true)}
            variant="outline"
            className="border-green-500 text-green-600 hover:bg-green-50"
          >
            SEO
          </Button>
          <Button
            icon="heroicons:pencil-square"
            onClick={handleEdit}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            Edit Subcategory
          </Button>
          <Button
            icon="heroicons:trash"
            onClick={handleDelete}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            Delete
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information */}
        <Card title="Subcategory Information" className="lg:col-span-2">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Title</label>
                <p className="text-sm font-medium text-gray-900 dark:text-white">{subcategory.title}</p>
              </div>

            </div>

            <div>
              <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Description</label>
              <p className="text-sm text-gray-900 dark:text-white">
                {subcategory.description || <span className="text-gray-400">No description available</span>}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Parent Category</label>
                {subcategory.category ? (
                  <Link
                    to={`/category/${subcategory.category.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-sm"
                  >
                    <Icon icon="heroicons:folder" className="w-3 h-3 mr-1" />
                    {subcategory.category.title}
                  </Link>
                ) : (
                  <span className="text-gray-400 text-sm">No parent category</span>
                )}
              </div>
              {/* <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Color</label>
                <div className="flex items-center gap-3">
                  <div
                    className="w-8 h-8 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                    style={{ backgroundColor: subcategory.color }}
                  />
                  <span className="text-sm font-mono text-gray-700 dark:text-gray-300">{subcategory.color}</span>
                </div>
              </div> */}
            </div>
          </div>
        </Card>

        {/* Stats & Info Card */}
        <Card title="Information" className="h-fit">
          <div className="space-y-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {subcategory.form_fields?.length || 0}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">Form Fields</div>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Created</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(subcategory.created_at).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Updated</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(subcategory.updated_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Media Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Images Card */}
        <Card title="Media" className="h-fit">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Main Image</label>
              {subcategory.image ? (
                <div className="relative h-48 bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                  <img
                    src={import.meta.env.VITE_ASSET_HOST_URL + subcategory.image}
                    alt={subcategory.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="h-48 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                  <div className="text-center">
                    <Icon icon="heroicons:photo" className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500 dark:text-gray-400">No image available</p>
                  </div>
                </div>
              )}
            </div>


          </div>
        </Card>

        {/* SEO Information Card - Moved to dedicated SEO modal */}
        {/* <Card title="SEO Information" className="h-fit">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Title</label>
              <p className="text-gray-900 dark:text-white">
                {subcategory.seo_title || <span className="text-gray-400">Not specified</span>}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Description</label>
              <p className="text-gray-900 dark:text-white text-sm">
                {subcategory.seo_description || <span className="text-gray-400">Not specified</span>}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Keywords</label>
              <p className="text-gray-900 dark:text-white text-sm">
                {subcategory.seo_keywords || <span className="text-gray-400">Not specified</span>}
              </p>
            </div>
          </div>
        </Card> */}
      </div>

      {/* Form Fields */}
      <Card>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Custom Form Fields</h2>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Dynamic input fields for data collection in this subcategory
            </p>
          </div>
          <Button
            icon="heroicons:plus"
            onClick={handleAddFormField}
            className="bg-primary-500 hover:bg-primary-600 text-white"
            size="sm"
          >
            Add Field
          </Button>
        </div>

        {subcategory.form_fields && subcategory.form_fields.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3">
            {subcategory.form_fields.map((field) => (
              <div key={field.id} className="group relative bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:shadow-md hover:bg-white dark:hover:bg-gray-800 transition-all duration-200">
                {/* Action buttons */}
                <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={() => handleEditFormField(field)}
                    className="p-1 bg-white dark:bg-gray-700 rounded text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-gray-200 dark:border-gray-600 shadow-sm"
                    title="Edit field"
                  >
                    <Icon icon="heroicons:pencil-square" className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleDeleteFormField(field)}
                    className="p-1 bg-white dark:bg-gray-700 rounded text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 border border-gray-200 dark:border-gray-600 shadow-sm"
                    title="Delete field"
                  >
                    <Icon icon="heroicons:trash" className="w-3 h-3" />
                  </button>
                </div>

                {/* Field Header */}
                <div className="mb-2 pr-14">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                      {field.label_name}
                    </h3>
                    <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs px-1.5 py-0.5">
                      {formatFieldType(field.input_type)}
                    </Badge>
                  </div>
                  {field.label_subtitle && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 truncate">{field.label_subtitle}</p>
                  )}
                </div>

                {/* Field Details */}
                <div className="space-y-1.5 text-xs">
                  {field.placeholder && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500 dark:text-gray-400">Placeholder:</span>
                      <span className="text-gray-900 dark:text-white truncate ml-2 max-w-[120px]" title={field.placeholder}>
                        {field.placeholder}
                      </span>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <span className="text-gray-500 dark:text-gray-400">Required:</span>
                    <Badge className={`text-xs px-1.5 py-0.5 ${field.is_required ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
                      {field.is_required ? "Yes" : "No"}
                    </Badge>
                  </div>

                  {field.options && field.options.length > 0 && (
                    <div className="pt-1.5 border-t border-gray-200 dark:border-gray-600">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-gray-500 dark:text-gray-400">Options:</span>
                        <span className="text-gray-400 text-xs">{field.options.length} items</span>
                      </div>
                      <div className="flex flex-wrap gap-1 max-h-16 overflow-y-auto">
                        {field.options.slice(0, 3).map((option, index) => (
                          <span
                            key={index}
                            className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded truncate max-w-[80px]"
                            title={option}
                          >
                            {option}
                          </span>
                        ))}
                        {field.options.length > 3 && (
                          <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 text-xs rounded">
                            +{field.options.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700 mb-3">
              <Icon icon="heroicons:document-text" className="h-6 w-6 text-gray-400" />
            </div>
            <h3 className="text-base font-medium text-gray-900 dark:text-white mb-2">No custom fields yet</h3>
            <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">
              Create dynamic form fields to collect specific data for this subcategory.
            </p>
            <Button
              icon="heroicons:plus"
              onClick={handleAddFormField}
              className="bg-primary-500 hover:bg-primary-600 text-white"
              size="sm"
            >
              Create Form Field
            </Button>
          </div>
        )}
      </Card>

      {/* Translations */}
      {subcategory.translations && subcategory.translations.length > 0 && (
        <Card title="Translations">
          <div className="space-y-4">
            {subcategory.translations.map((translation) => (
              <div key={translation.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {translation.language.toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {translation.language === 'bn' ? 'Bengali' : translation.language === 'ar' ? 'Arabic' : translation.language} Translation
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Title</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.title || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div>
                  {/* <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Title</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.seo_title || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div> */}
                  <div className="md:col-span-2">
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Description</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.description || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div>
                  {/* <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Description</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.seo_description || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Keywords</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.seo_keywords || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div> */}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Delete Modal */}
      {showDeleteModal && (
        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={subcategory}
        />
      )}

      {/* Add Form Field Modal */}
      {showAddFormFieldModal && (
        <AddFormFieldModal
          showModal={showAddFormFieldModal}
          setShowModal={setShowAddFormFieldModal}
          subcategoryId={id}
          onSuccess={handleFormFieldAdded}
        />
      )}

      {/* Edit Form Field Modal */}
      {showEditFormFieldModal && selectedFormField && (
        <EditFormFieldModal
          showModal={showEditFormFieldModal}
          setShowModal={setShowEditFormFieldModal}
          formField={selectedFormField}
          onSuccess={handleFormFieldAdded}
        />
      )}

      {/* Delete Form Field Modal */}
      {showDeleteFormFieldModal && selectedFormField && (
        <DeleteFormFieldModal
          showModal={showDeleteFormFieldModal}
          setShowModal={setShowDeleteFormFieldModal}
          formFieldId={selectedFormField.id}
          formFieldName={selectedFormField.label_name}
          onSuccess={handleFormFieldAdded}
        />
      )}

      {/* SEO Modal */}
      {seoModalOpen && (
        <SeoModal
          isOpen={seoModalOpen}
          onClose={() => setSeoModalOpen(false)}
          data={subcategory}
          type="subcategory"
          onSuccess={() => {
            // Optionally refetch data or update local state
            refetch(); // Use existing refetch function
          }}
        />
      )}

      {/* Image Zoom Modal */}
      {imageZoomOpen && subcategory.image && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[99999] p-4"
          onClick={() => setImageZoomOpen(false)}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setImageZoomOpen(false)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2"
              title="Close"
            >
              <Icon icon="heroicons:x-mark" className="w-6 h-6" />
            </button>
            <img
              src={import.meta.env.VITE_ASSET_HOST_URL + subcategory.image}
              alt={subcategory.title}
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="absolute bottom-4 left-4 right-4 text-center">
              <p className="text-white bg-black bg-opacity-50 rounded px-3 py-1 inline-block">
                {subcategory.title}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubCategoryDetails;
