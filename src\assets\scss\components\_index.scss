// This file will be used in the future to replace @import with @use and @forward
// For now, we're keeping the @import syntax in app.scss to avoid breaking changes
// When ready to migrate, you can use this file with @forward for all component styles

@forward "table.scss";
@forward "map.scss";
@forward "progress";
@forward "tippy";
@forward "swiper";
@forward "alert";
@forward "card";
@forward "auth";
@forward "button";
@forward "badge";
@forward "typography";
@forward "form";
@forward "input-group";
@forward "react-select";
@forward "pagination";
@forward "breadcrumbs";
@forward "print.scss";
