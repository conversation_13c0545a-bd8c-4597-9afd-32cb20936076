import { useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from "react-redux";
import { setUser, setToken } from "@/store/api/auth/authSlice";

const PublicOutlet = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuth } = useSelector((state) => state.auth);

  useEffect(() => {
    const tokenFromCookie = document.cookie
      .split('; ')
      .find((cookie) => cookie.startsWith('accessToken='));

    if (isAuth || tokenFromCookie) {
      const tokenValue = tokenFromCookie?.split('=')[1];
      if (tokenValue) dispatch(setToken(tokenValue));

      const storedUser = localStorage.getItem('user');
      if (storedUser) dispatch(setUser(JSON.parse(storedUser)));

      navigate('/dashboard', { replace: true });
    }
  }, [isAuth, dispatch, navigate]);

  return <Outlet />;
};

export default PublicOutlet;
