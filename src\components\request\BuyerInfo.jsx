import React from "react";
import { Link } from "react-router-dom";
import Icon from "@/components/ui/Icon";

const BuyerInfo = ({ buyer }) => {
  if (!buyer) return null;

  // Get avatar URL or use default
  const getAvatarUrl = () => {
    if (buyer.profile_picture_url) {
      return `${import.meta.env.VITE_ASSET_HOST_URL}${buyer.profile_picture_url}`;
    }
    return "https://ui-avatars.com/api/?name=" + encodeURIComponent(`${buyer.first_name} ${buyer.last_name}`);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'danger';
      case 'pending':
        return 'warning';
      default:
        return 'gray';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Buyer Information</h3>
      
      <div className="flex items-start space-x-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <img 
            src={getAvatarUrl()} 
            alt={`${buyer.first_name} ${buyer.last_name}`} 
            className="h-16 w-16 rounded-full object-cover border-2 border-gray-200"
          />
        </div>
        
        {/* Buyer Details */}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <Link 
              to={`/user/${buyer.id}`} 
              className="text-lg font-medium text-blue-600 hover:text-blue-800 hover:underline"
            >
              {buyer.first_name} {buyer.last_name}
            </Link>
            <span className={`px-2 py-1 text-xs rounded-full bg-${getStatusColor(buyer.status)}-100 text-${getStatusColor(buyer.status)}-800`}>
              {buyer.status}
            </span>
          </div>
          
          <div className="mt-2 space-y-1 text-sm text-gray-600">
            <div className="flex items-center">
              <Icon icon="heroicons-outline:mail" className="h-4 w-4 mr-2" />
              <a href={`mailto:${buyer.email}`} className="hover:text-blue-600">{buyer.email}</a>
            </div>
            
            <div className="flex items-center">
              <Icon icon="heroicons-outline:phone" className="h-4 w-4 mr-2" />
              <a href={`tel:${buyer.phone_number}`} className="hover:text-blue-600">{buyer.phone_number}</a>
            </div>
            
          
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyerInfo;
